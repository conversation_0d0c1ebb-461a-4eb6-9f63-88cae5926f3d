#!/bin/bash

echo "=== AgentBoard 额外依赖安装脚本 ==="
echo "正在为AgentBoard安装评估任务所需的额外依赖..."

# 确保在AgentBoard环境中
if [[ "$CONDA_DEFAULT_ENV" != "AgentBoard" ]]; then
    echo "警告: 当前不在AgentBoard环境中，请先运行: conda activate AgentBoard"
    exit 1
fi

echo ""
echo "1. 安装文本游戏相关依赖 (ALFWorld, Jericho)..."
pip install textworld
pip install jericho

echo ""
echo "2. 安装BabyAI相关依赖..."
pip install babyai-text
pip install gym-minigrid

echo ""
echo "3. 安装科学世界依赖..."
pip install scienceworld

echo ""
echo "4. 安装PDDL规划依赖..."
pip install pddlgym

echo ""
echo "5. 安装网页浏览相关依赖..."
pip install playwright
pip install beautifulsoup4
pip install selenium
pip install requests

echo ""
echo "6. 安装数据处理依赖..."
pip install pandas
pip install numpy
pip install networkx
pip install jsonlines

echo ""
echo "7. 安装可视化依赖..."
pip install matplotlib
pip install seaborn

echo ""
echo "8. 安装其他工具依赖..."
pip install python-dotenv
pip install pyyaml
pip install tqdm

echo ""
echo "9. 初始化Playwright浏览器..."
playwright install

echo ""
echo "=== 依赖安装完成 ==="
echo ""
echo "已安装的主要依赖："
echo "✅ textworld - 文本游戏环境"
echo "✅ jericho - 交互式小说游戏"
echo "✅ babyai-text - BabyAI文本环境"
echo "✅ scienceworld - 科学世界环境"
echo "✅ pddlgym - PDDL规划环境"
echo "✅ playwright - 网页自动化"
echo "✅ 数据处理和可视化工具"
echo ""
echo "现在您可以运行更多的AgentBoard评估任务了！"
