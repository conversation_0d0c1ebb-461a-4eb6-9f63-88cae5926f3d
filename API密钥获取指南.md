# AgentBoard 工具API密钥获取指南

## 概述

AgentBoard的某些评估任务需要第三方API密钥。以下是各种API的获取方式和配置方法。

## 🔑 需要的API密钥

### 1. 电影数据库API (MOVIE_KEY)

**用途**: 电影推荐和查询任务

**获取方式**:
- **The Movie Database (TMDb)** - 推荐
  - 网站: https://www.themoviedb.org/
  - 注册账号 → 设置 → API → 申请API密钥
  - 免费，每天40,000次请求
  - 示例: `eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJ...`

- **OMDb API** - 备选
  - 网站: http://www.omdbapi.com/
  - 免费版每天1000次请求
  - 示例: `12345678`

### 2. 天气API (WEATHER_KEY)

**用途**: 天气查询和预报任务

**获取方式**:
- **OpenWeatherMap** - 推荐
  - 网站: https://openweathermap.org/api
  - 注册账号 → My API keys → 创建密钥
  - 免费版每分钟60次调用，每天1000次
  - 示例: `abcd1234567890efgh`

- **WeatherAPI** - 备选
  - 网站: https://www.weatherapi.com/
  - 免费版每月100万次调用
  - 示例: `key-1234567890abcdef`

### 3. 学术数据库API (可选)

**用途**: 学术论文和引用网络分析

**获取方式**:
- **Semantic Scholar API**
  - 网站: https://www.semanticscholar.org/product/api
  - 免费，无需密钥（有速率限制）
  - 如需更高配额可申请API密钥

- **CrossRef API**
  - 网站: https://www.crossref.org/services/metadata-delivery/rest-api/
  - 免费，建议提供邮箱作为标识

### 4. 其他可选API

**Google Custom Search API** (用于网页搜索任务):
- 网站: https://developers.google.com/custom-search/v1/introduction
- 免费版每天100次查询

**News API** (用于新闻相关任务):
- 网站: https://newsapi.org/
- 免费版每月1000次请求

## 📝 配置方法

### 方法1: 使用.env文件 (推荐)

在AgentBoard根目录创建`.env`文件：

```bash
# 创建.env文件
cat > .env << 'EOF'
# OpenAI API (已配置)
OPENAI_API_KEY=sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb
OPENAI_API_BASE=https://api.chatanywhere.tech/v1

# 电影API
MOVIE_KEY=your_tmdb_api_key_here

# 天气API
WEATHER_KEY=your_openweathermap_api_key_here

# 学术API (可选)
SEMANTIC_SCHOLAR_API_KEY=your_semantic_scholar_key_here

# 其他API (可选)
GOOGLE_SEARCH_API_KEY=your_google_search_key_here
NEWS_API_KEY=your_news_api_key_here

# 项目路径
PROJECT_PATH=/home/<USER>/mgq/2025_5/AgentBoard
EOF
```

### 方法2: 环境变量设置

```bash
# 添加到setup_custom_api.sh
export MOVIE_KEY="your_tmdb_api_key_here"
export WEATHER_KEY="your_openweathermap_api_key_here"
```

## 🧪 测试API连接

创建API测试脚本：

```python
# test_tool_apis.py
import os
import requests
from dotenv import load_dotenv

def test_movie_api():
    """测试电影API"""
    load_dotenv()
    api_key = os.getenv('MOVIE_KEY')
    if not api_key:
        print("❌ MOVIE_KEY未设置")
        return False
    
    # 测试TMDb API
    url = f"https://api.themoviedb.org/3/movie/popular?api_key={api_key}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            print("✅ 电影API连接成功")
            return True
        else:
            print(f"❌ 电影API错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 电影API连接失败: {e}")
        return False

def test_weather_api():
    """测试天气API"""
    load_dotenv()
    api_key = os.getenv('WEATHER_KEY')
    if not api_key:
        print("❌ WEATHER_KEY未设置")
        return False
    
    # 测试OpenWeatherMap API
    url = f"http://api.openweathermap.org/data/2.5/weather?q=Beijing&appid={api_key}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            print("✅ 天气API连接成功")
            return True
        else:
            print(f"❌ 天气API错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 天气API连接失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 工具API连接测试 ===")
    test_movie_api()
    test_weather_api()
```

## 📋 任务与API对应关系

| 任务类型 | 需要的API | 必需程度 | 说明 |
|---------|----------|----------|------|
| tool-query | MOVIE_KEY, WEATHER_KEY | 必需 | 工具查询任务 |
| tool-operation | MOVIE_KEY, WEATHER_KEY | 必需 | 工具操作任务 |
| alfworld | 无 | - | 家庭环境任务 |
| babyai | 无 | - | 导航任务 |
| webshop | 无 | - | 购物任务 |
| webarena | 无 | - | 网页浏览任务 |
| jericho | 无 | - | 文本游戏任务 |
| pddl | 无 | - | 规划任务 |
| scienceworld | 无 | - | 科学推理任务 |

## 🎯 推荐的获取顺序

1. **立即获取** (运行工具任务必需):
   - TMDb API密钥 (电影)
   - OpenWeatherMap API密钥 (天气)

2. **可选获取** (增强功能):
   - Google Custom Search API
   - News API
   - Semantic Scholar API

## 💡 使用建议

### 免费配额管理
- 监控API使用量
- 设置合理的请求间隔
- 使用缓存减少重复请求

### 安全建议
- 不要在代码中硬编码API密钥
- 使用.env文件并添加到.gitignore
- 定期轮换API密钥

### 测试建议
- 先用少量数据测试
- 验证API响应格式
- 处理API限流和错误

## 📞 获取帮助

如果在API获取过程中遇到问题：

1. **查看官方文档**: 每个API提供商都有详细的文档
2. **检查配额限制**: 确认免费版本的限制
3. **测试连接**: 使用提供的测试脚本验证
4. **查看错误日志**: AgentBoard会记录API调用错误

获取API密钥后，请按照配置方法设置，然后就可以运行完整的AgentBoard评估任务了！
