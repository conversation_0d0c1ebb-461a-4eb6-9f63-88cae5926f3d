#!/bin/bash

# AgentBoard 中转API配置脚本
echo "=== AgentBoard 中转API配置 ==="

# 设置环境变量
export OPENAI_API_KEY="sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb"

# 设置中转API的base URL
export OPENAI_API_BASE="https://api.chatanywhere.tech/v1"

# 设置项目路径
export PROJECT_PATH="$(pwd)"

echo "环境变量设置完成："
echo "OPENAI_API_KEY: ${OPENAI_API_KEY:0:10}..."
echo "OPENAI_API_BASE: ${OPENAI_API_BASE:-"默认OpenAI官方API"}"
echo "PROJECT_PATH: $PROJECT_PATH"

# 创建结果目录
mkdir -p results/custom_test
mkdir -p results/custom_test/logs

echo ""
echo "=== 可用的评估命令 ==="
echo ""
echo "1. 测试工具使用任务（推荐开始）："
echo "python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query --model gpt-3.5-turbo --log_path ./results/custom_test"
echo ""
echo "2. 测试ALFWorld任务："
echo "python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks alfworld --model gpt-3.5-turbo --log_path ./results/custom_test"
echo ""
echo "3. 测试BabyAI任务："
echo "python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks babyai --model gpt-3.5-turbo --log_path ./results/custom_test"
echo ""
echo "4. 使用GPT-4模型："
echo "python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query --model gpt-4 --log_path ./results/custom_test"
echo ""
echo "5. 运行多个任务："
echo "python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query alfworld --model gpt-3.5-turbo --log_path ./results/custom_test"
echo ""
echo "注意："
echo "- 如果您的中转API需要特定的base URL，请修改此脚本中的OPENAI_API_BASE变量"
echo "- WebShop任务需要先启动WebShop服务器"
echo "- 某些任务可能需要额外的依赖安装"
echo ""
