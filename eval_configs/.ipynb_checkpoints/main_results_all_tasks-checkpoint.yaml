
run:
  max_num_steps: 30
  wandb: True
  project_name: debug-llm-agent-eval-run-1130
  baseline_dir: data/baseline_results_details
  log_path: ${PROJECT_PATH}/results/llama2-13b-wandb-full-1128

agent:
  name: VanillaAgent
  memory_size: 100
  need_goal: True

llm:
  gpt-35-turbo:
      name: gpt_azure
      engine: gpt-35-turbo
      context_length: 4096
      use_azure: True
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      stop: "\n"
      use_parser: False
  text-davinci-003:
      name: gpt_azure
      engine: text-davinci-003
      context_length: 4096
      use_azure: True
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      stop: "\n"
      use_parser: False
  gpt4:
      name: gpt_azure
      engine: gpt4
      context_length: 8192
      use_azure: True
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      stop: "\n"
      use_parser: False
  gpt-3.5-turbo-16k:
      name: gpt
      engine: gpt-3.5-turbo-16k
      context_length: 16384 # 4096
      use_azure: False
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      stop: "\n"
      use_parser: False
  gpt-3.5-turbo-1106:
      name: gpt
      engine: gpt-3.5-turbo-1106
      context_length: 4096
      use_azure: False
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      stop: "\n"
      use_parser: False
  claude2:
      name: claude
      engine: claude-2
      temperature: 0.
      top_p: 1
      retry_delays: 10
      max_retry_iters: 15
      stop:
        - "\n\nHuman:"
      context_length: 100000
      xml_split:
        example: ["\n<example>\n", "</example>\n"]
        text: ["<text>\n", "</text>\n"]
        rule: ["<rule>\n", "</rule>\n"]
        system_msg: ["<system_message>\n", "</system_message>\n"]
        instruction: ["<instruction>\n", "</instruction>\n"]
        goal: ["<goal>\n", "</goal>\n"]
      use_parser: False
  codellama-13b:
      name: vllm
      engine: codellama/CodeLlama-13b-Instruct-hf
      # engine: /zhangjunlei/download/models/codellama-13b
      max_tokens: 100
      temperature: 0
      top_p: 1
      # stop: '\n'
      context_length: 16384
      dtype: float32
      ngpu: 4
      use_parser: True
  codellama-34b:
      name: hg
      #engine: codellama/CodeLlama-13b-Instruct-hf
      engine: /zhangjunlei/download/models/codellama-34b
      max_tokens: 100
      temperature: 0
      top_p: 1
      # stop: '\n'
      context_length: 16384
      dtype: float32
      ngpu: 4
      use_parser: True
  lemur-70b:
      name: vllm
      engine: OpenLemur/lemur-70b-chat-v1
      # engine: /zhangjunlei/download/models/lemur-70b-chat-v1/
      max_tokens: 100
      temperature: 0.
      top_p: 1
      stop:
      context_length: 4096
      ngpu: 4
      dtype: float16
      use_parser: True
  llama2-13b:
      name: vllm
      engine: meta-llama/Llama-2-13b-chat-hf
      # engine: /zhangjunlei/download/models/Llama-2-13b-chat-hf
      max_tokens: 100
      temperature: 0.
      top_p: 1
      stop: '\n'
      context_length: 4096
      dtype: float16
      ngpu: 2
      use_parser: True
  llama2-70b:
      name: vllm
      engine: /zhangjunlei/download/models/LLama-2-70b-chat-hf/Llama-2-70b-chat-hf
      #engine: meta-llama/Llama-2-70b-chat-hf
      # engine: /zhangjunlei/download/models/Llama-2-70b-chat-hf
      max_tokens: 100
      temperature: 0.
      top_p: 1
      stop: '\n'
      context_length: 4096
      dtype: float16
      ngpu: 4
      use_parser: True

  vicuna-13b-16k:
      name: vllm
      engine: lmsys/vicuna-13b-v1.5-16k
      # engine: /zhangjunlei/download/models/vicuna-13b-chat-hf
      max_tokens: 100
      temperature: 0.
      top_p: 1
      stop: '\n'
      context_length: 16384
      dtype: float16
      ngpu: 2
      use_parser: True

env:
  pddl:
    name: pddl
    game_name: [gripper, blockworld, barman, tyreworld]
    env_num_per_task: 20
    check_actions: "check valid actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/pddl_vanilla_prompt.json
    label_path: ${PROJECT_PATH}/data/pddl/test.jsonl

  jericho:
    name: jericho
    game_name: ["905", "acorncourt", "afflicted","balances","dragon","jewel","library","omniquest","reverb","snacktime","zenon","zork1","zork2", "zork3","detective","night","pentari","weapon","huntdark","loose"]
    game_dir: ${PROJECT_PATH}/data/jericho/z-machine-games-master/jericho-game-suite
    label_path: ${PROJECT_PATH}/data/jericho/test.jsonl
    check_actions: "check valid actions"
    check_inventory: "inventory"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/jericho_vanilla_prompt.json


  babyai:
    name: babyai
    seed: 1234
    game_level: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 25, 27, 28, 29, 31, 32]
    label_path: ${PROJECT_PATH}/data/babyai/test.jsonl
    env_num_per_task: 4
    check_actions: "check valid actions"
    check_inventory: "inventory"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/babyai_vanilla_prompt.json

  tool-query:
    name: tool-query
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data
    result_dir: ${PROJECT_PATH}/results/query
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/
  
  tool-operation:
    name: tool-operation
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data
    result_dir: ${PROJECT_PATH}/results/operation
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/

  tool-academia:
    name: academia
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data/tools
    result_dir: ${PROJECT_PATH}/results/academia
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/academia/academia_vanilla_prompt.json

  tool-movie:
    name: movie
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data/tools
    result_dir: ${PROJECT_PATH}/results/movie
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/movie/movie_vanilla_prompt.json

  tool-sheet:
    name: sheet
    seed: 1234
    dataset_name: sheet
    dataset_dir: ${PROJECT_PATH}/data/tools
    result_dir: ${PROJECT_PATH}/results/sheet
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/sheet/sheet_vanilla_prompt.json

  tool-todo:
    name: todo
    seed: 1234
    dataset_name: todo
    dataset_dir: ${PROJECT_PATH}/data/tools
    result_dir: ${PROJECT_PATH}/results/todo
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/todo/todo_vanilla_prompt.json

  tool-weather:
    name: weather
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data/tools
    result_dir: ${PROJECT_PATH}/results/weather
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/weather/weather_vanilla_prompt.json

  scienceworld:
    name: scienceworld
    envStepLimit: 30
    seed: 0
    check_inventory: True
    check_actions: check valid actions
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/scienceworld_base.json
    label_path: ${PROJECT_PATH}/data/scienceworld/test.jsonl


  alfworld:
    name: alfworld
    base_config: ${PROJECT_PATH}/environment/alfworld/base_config.yaml
    split: eval_out_of_distribution
    batch_size: 1
    label_path: ${PROJECT_PATH}/data/alfworld/test.jsonl
    check_inventory: True
    check_actions: check valid actions
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/alfworld_base.json
  
  webshop:
    name: webshop
    web_url: http://127.0.0.1:3000
    subreward_floder: ${PROJECT_PATH}/WebShop/subreward_log
    max_step: 50
    start_idx: 0
    end_idx: 251
    to_print: True
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/webshop_vanilla.json
    label_path: ${PROJECT_PATH}/data/webshop/test.jsonl

  webarena:
    name: webarena
    render: True
    headless: True
    slow_mo: 100
    observation_type: accessibility_tree
    current_viewport_only: True
    viewport_size: {"width": 1280, "height": 720}
    save_trace_enabled: True
    sleep_after_execution: 2.5
    action_set_tag: id_accessibility_tree
    render_screenshot: True
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/webbrowse_vanilla.json
    label_path: ${PROJECT_PATH}/data/webarena/test.jsonl
    start_test_id: 0
    test_case_count: 245
    parsing_failure_th: 5
    repeating_action_failure_th: 5
