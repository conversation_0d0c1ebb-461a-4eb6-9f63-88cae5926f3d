run:
  max_num_steps: 30
  wandb: False  # 设置为False避免需要wandb配置
  project_name: eval-test
  baseline_dir: data/baseline_results
  log_path: ${PROJECT_PATH}/results/custom_test

agent:
  name: VanillaAgent
  memory_size: 100
  need_goal: True

llm:
  # 中转API配置 - GPT-3.5-turbo
  gpt-3.5-turbo:
      name: gpt
      engine: gpt-3.5-turbo
      context_length: 4096
      use_azure: False  # 使用标准OpenAI接口而非Azure
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      max_tokens: 200
      stop: "\n"
      use_parser: False

  # 中转API配置 - GPT-4
  gpt-4:
      name: gpt
      engine: gpt-4
      context_length: 8192
      use_azure: False  # 使用标准OpenAI接口而非Azure
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      max_tokens: 200
      stop: "\n"
      use_parser: False

  # 中转API配置 - GPT-3.5-turbo-16k
  gpt-3.5-turbo-16k:
      name: gpt
      engine: gpt-3.5-turbo-16k
      context_length: 16384
      use_azure: False
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      max_tokens: 200
      stop: "\n"
      use_parser: False

  # 中转API配置 - GPT-4o-ca
  gpt-4o-ca:
      name: gpt
      engine: gpt-4o-ca
      context_length: 128000
      use_azure: False
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      max_tokens: 200
      stop: "\n"
      use_parser: False

  # 中转API配置 - Claude Sonnet 4
  claude-sonnet-4-20250514:
      name: claude
      engine: claude-sonnet-4-20250514
      context_length: 200000
      use_azure: False
      temperature: 0.
      top_p: 1
      retry_delays: 20
      max_retry_iters: 15
      max_tokens: 500
      stop: "\n"
      use_parser: False

env:
  # WebShop任务配置（相对简单，适合测试）
  webshop:
    name: webshop
    web_url: http://127.0.0.1:3000
    subreward_floder: ${PROJECT_PATH}/agentboard/WebShop/subreward_log
    max_step: 50
    start_idx: 0
    end_idx: 5  # 运行5个任务进行测试
    to_print: True
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/VanillaAgent/webshop_vanilla.json
    label_path: ${PROJECT_PATH}/data/webshop/test.jsonl

  # Tool任务配置（不需要额外环境设置）
  tool-query:
    name: tool-query
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data
    result_dir: ${PROJECT_PATH}/results/query
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts

  tool-operation:
    name: tool-operation
    seed: 1234
    dataset_dir: ${PROJECT_PATH}/data
    result_dir: ${PROJECT_PATH}/results/operation
    check_actions: "check_valid_actions"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts

  # ALFWorld任务配置
  alfworld:
    name: alfworld
    base_config: ${PROJECT_PATH}/agentboard/environment/alfworld/base_config.yaml
    split: eval_out_of_distribution
    batch_size: 1
    label_path: ${PROJECT_PATH}/data/alfworld/test.jsonl
    check_inventory: True
    check_actions: check valid actions
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/VanillaAgent/alfworld_base.json

  # BabyAI任务配置
  babyai:
    name: babyai
    seed: 1234
    game_level: [1, 2, 3, 4, 5]  # 减少测试级别
    label_path: ${PROJECT_PATH}/data/babyai/test.jsonl
    env_num_per_task: 2  # 减少每个任务的环境数量
    check_actions: "check valid actions"
    check_inventory: "inventory"
    init_prompt_path: ${PROJECT_PATH}/agentboard/prompts/VanillaAgent/babyai_vanilla_prompt.json
