# AgentBoard 中转API使用指南

## 概述

本指南将帮助您使用中转OpenAI API来运行AgentBoard评估。您的API密钥已经配置在相关文件中。

## 快速开始

### 1. 环境准备

```bash
# 给脚本执行权限
chmod +x setup_custom_api.sh

# 运行配置脚本
source setup_custom_api.sh
```

### 2. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 如果需要运行特定任务，可能需要额外安装：
pip install openai tiktoken wandb
```

### 3. 配置中转API地址

如果您的中转API需要特定的base URL，请编辑 `setup_custom_api.sh` 文件，取消注释并修改这一行：

```bash
export OPENAI_API_BASE="https://your-proxy-url.com/v1"
```

常见的中转API格式：
- `https://api.openai-proxy.com/v1`
- `https://your-domain.com/v1`
- `https://api.chatanywhere.com.cn/v1`

### 4. 运行评估

#### 推荐的第一次测试（工具使用任务）：

```bash
python agentboard/eval_main.py \
    --cfg-path eval_configs/custom_openai_config.yaml \
    --tasks tool-query \
    --model gpt-3.5-turbo \
    --log_path ./results/custom_test
```

#### 其他可用任务：

```bash
# ALFWorld任务
python agentboard/eval_main.py \
    --cfg-path eval_configs/custom_openai_config.yaml \
    --tasks alfworld \
    --model gpt-3.5-turbo \
    --log_path ./results/custom_test

# BabyAI任务
python agentboard/eval_main.py \
    --cfg-path eval_configs/custom_openai_config.yaml \
    --tasks babyai \
    --model gpt-3.5-turbo \
    --log_path ./results/custom_test

# 使用GPT-4
python agentboard/eval_main.py \
    --cfg-path eval_configs/custom_openai_config.yaml \
    --tasks tool-query \
    --model gpt-4 \
    --log_path ./results/custom_test

# 运行多个任务
python agentboard/eval_main.py \
    --cfg-path eval_configs/custom_openai_config.yaml \
    --tasks tool-query alfworld \
    --model gpt-3.5-turbo \
    --log_path ./results/custom_test
```

## 配置说明

### 已配置的模型

在 `eval_configs/custom_openai_config.yaml` 中已配置：

1. **gpt-3.5-turbo** - 标准GPT-3.5模型
2. **gpt-4** - GPT-4模型
3. **gpt-3.5-turbo-16k** - 长上下文GPT-3.5模型

### 已配置的任务

1. **tool-query** - 工具查询任务（推荐开始）
2. **tool-operation** - 工具操作任务
3. **alfworld** - ALFWorld家庭环境任务
4. **babyai** - BabyAI导航任务
5. **webshop** - 网购任务（需要额外设置）

### 配置特点

- 关闭了WandB以避免额外配置
- 减少了测试数量以节省API调用
- 使用标准OpenAI接口而非Azure
- 设置了合理的重试机制

## 查看结果

### 日志文件位置

- 详细日志：`./results/custom_test/logs/`
- 汇总结果：`./results/custom_test/all_results.txt`

### 结果解读

评估完成后，您会看到以下指标：

- **Success Rate**: 任务成功完成率
- **Progress Rate**: 任务进度完成率
- **Grounding Accuracy**: 基础准确率
- **Easy/Hard SR/PR**: 按难度分级的成功率和进度率

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error: OPENAI_API_KEY environment variable not set
   ```
   解决：确保运行了 `source setup_custom_api.sh`

2. **API地址错误**
   ```
   Error: Connection error
   ```
   解决：检查并设置正确的 `OPENAI_API_BASE`

3. **模型不可用**
   ```
   Error: Model not found
   ```
   解决：确认您的中转API支持所请求的模型

4. **依赖缺失**
   ```
   ModuleNotFoundError
   ```
   解决：安装缺失的依赖包

### 调试技巧

1. **测试API连接**：
   ```python
   import openai
   import os
   
   openai.api_key = "sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb"
   # openai.api_base = "your-proxy-url"  # 如果需要
   
   response = openai.ChatCompletion.create(
       model="gpt-3.5-turbo",
       messages=[{"role": "user", "content": "Hello"}],
       max_tokens=10
   )
   print(response.choices[0].message.content)
   ```

2. **查看详细错误**：
   在命令中添加 `--verbose` 或查看日志文件

3. **减少测试规模**：
   修改配置文件中的 `end_idx` 或 `env_num_per_task` 参数

## 高级配置

### 自定义任务数量

编辑 `eval_configs/custom_openai_config.yaml`：

```yaml
env:
  webshop:
    end_idx: 5  # 只测试前5个样本
  babyai:
    env_num_per_task: 1  # 每个任务只测试1个环境
```

### 调整模型参数

```yaml
llm:
  gpt-3.5-turbo:
    temperature: 0.1  # 增加随机性
    max_tokens: 500   # 增加输出长度
    retry_delays: 30  # 增加重试间隔
```

### 启用WandB可视化

```yaml
run:
  wandb: True
  project_name: "my-agentboard-test"
```

然后设置WandB API密钥：
```bash
export WANDB_API_KEY="your-wandb-key"
```

## 联系支持

如果遇到问题，请检查：
1. API密钥是否正确
2. 中转API地址是否正确
3. 网络连接是否正常
4. 依赖是否完整安装

祝您使用愉快！
