#!/bin/bash

echo "=== AgentBoard 快速依赖安装脚本 ==="
echo "安装基础依赖，跳过复杂的编译包..."

# 确保在AgentBoard环境中
if [[ "$CONDA_DEFAULT_ENV" != "AgentBoard" ]]; then
    echo "警告: 当前不在AgentBoard环境中，请先运行: conda activate AgentBoard"
    exit 1
fi

echo ""
echo "1. 安装数据处理依赖..."
pip install pandas numpy jsonlines

echo ""
echo "2. 安装环境变量管理..."
pip install python-dotenv

echo ""
echo "3. 安装网页处理依赖..."
pip install requests

echo ""
echo "4. 检查已有依赖..."
echo "检查OpenAI..."
python -c "import openai; print('✅ OpenAI已安装')" 2>/dev/null || pip install openai

echo "检查tiktoken..."
python -c "import tiktoken; print('✅ tiktoken已安装')" 2>/dev/null || pip install tiktoken

echo "检查yaml..."
python -c "import yaml; print('✅ PyYAML已安装')" 2>/dev/null || pip install pyyaml

echo ""
echo "=== 快速依赖安装完成 ==="
echo ""
echo "已安装的依赖："
echo "✅ pandas, numpy, jsonlines - 数据处理"
echo "✅ python-dotenv - 环境变量管理"
echo "✅ requests - HTTP请求"
echo "✅ openai, tiktoken - OpenAI API"
echo "✅ pyyaml - 配置文件处理"
echo ""
echo "现在您可以："
echo "1. 配置API密钥"
echo "2. 运行基本的AgentBoard功能"
echo "3. 测试LLM和智能体"
echo ""
echo "注意: 某些高级任务可能需要额外依赖，可以按需安装"
