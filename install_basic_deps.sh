#!/bin/bash

echo "=== AgentBoard 基础依赖安装脚本 ==="
echo "安装不需要编译的基础依赖包..."

# 确保在AgentBoard环境中
if [[ "$CONDA_DEFAULT_ENV" != "AgentBoard" ]]; then
    echo "警告: 当前不在AgentBoard环境中，请先运行: conda activate AgentBoard"
    exit 1
fi

echo ""
echo "1. 安装数据处理依赖..."
pip install pandas numpy jsonlines

echo ""
echo "2. 安装网页处理依赖..."
pip install beautifulsoup4 requests

echo ""
echo "3. 安装环境变量管理..."
pip install python-dotenv

echo ""
echo "4. 安装可视化依赖..."
pip install matplotlib seaborn

echo ""
echo "5. 安装其他工具依赖..."
pip install pyyaml

echo ""
echo "6. 尝试安装BabyAI相关依赖..."
pip install babyai-text || echo "BabyAI安装失败，可能需要手动安装"

echo ""
echo "7. 尝试安装科学世界依赖..."
pip install scienceworld || echo "ScienceWorld安装失败，可能需要手动安装"

echo ""
echo "=== 基础依赖安装完成 ==="
echo ""
echo "已安装的依赖："
echo "✅ pandas, numpy, jsonlines - 数据处理"
echo "✅ beautifulsoup4, requests - 网页处理"
echo "✅ python-dotenv - 环境变量管理"
echo "✅ matplotlib, seaborn - 可视化"
echo "✅ pyyaml - 配置文件处理"
echo ""
echo "注意: textworld等复杂依赖可能需要单独安装"
echo "您现在可以测试基本功能了！"
