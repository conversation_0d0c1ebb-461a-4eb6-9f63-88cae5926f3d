gymnasium
playwright==1.32.1
Pillow
evaluate
openai==0.28.1
types-tqdm
tiktoken
prompt_toolkit
aiolimiter
beartype==0.12.0
minigrid==2.3.0
nltk
tenacity
timeout-decorator
gradio
gym==0.24.0
werkzeug==2.2.2
gspread
imageio
scikit-image
geopy
jsonlines
vllm
torch==2.0.1 # vllm 0.2.1.post1 requires
python-dotenv
bs4
deepspeed
scienceworld
torchvision==0.15.2
wandb
plotly
accelerate

beautifulsoup4==4.11.1
cleantext==1.1.4
env==0.1.0
Flask==2.1.2
gdown
gradio
gym==0.24.0
numpy==1.22.4
pandas==1.4.2
pyserini==0.17.0
pydantic==1.10.8
pytest
PyYAML==6.0
rank_bm25==0.2.2
requests==2.27.1
requests_mock
rich==12.4.4
scikit_learn==1.1.1
selenium==4.2.0
spacy==3.4.4
thefuzz==0.19.0
# torch==1.11.0 # vllm need torch 2.0.1 / claude need torch 1.11.0
tqdm==4.64.0
train==0.0.5
# transformers==4.23.1 # claude need transformers 4.23.1 / transformers 4.34.1
typing_extensions==4.5.0
typing-inspect==0.8.0
anthropic==0.6.0
