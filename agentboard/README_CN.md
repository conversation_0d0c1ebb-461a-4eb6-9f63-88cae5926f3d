# AgentBoard 项目结构说明文档

## 项目概述

AgentBoard 是一个用于评估大语言模型智能体（LLM Agent）性能的综合评估框架。该框架支持多种任务环境、智能体类型和语言模型，提供了完整的评估流程和可视化功能。

## 目录结构详解

### 1. 核心模块

#### 1.1 主入口文件
- **`eval_main.py`** - 主评估脚本
  - 功能：整个评估流程的入口点
  - 支持的任务：alfworld, jericho, pddl, webshop, webarena, tool-query, tool-operation, babyai, scienceworld
  - 主要功能：
    - 解析命令行参数和配置文件
    - 加载语言模型和智能体
    - 执行任务评估
    - 记录和可视化结果
    - 支持 WandB 集成

#### 1.2 智能体模块 (`agents/`)
- **`base_agent.py`** - 智能体基类
  - 定义了所有智能体必须实现的接口
  - 包含 reset、update、run 等核心方法

- **`vanilla_agent.py`** - 基础智能体实现
  - 最简单的智能体类型
  - 直接使用语言模型进行推理

- **`react_agent.py`** - ReAct智能体实现
  - 实现了 Reasoning + Acting 模式
  - 支持思考-行动-观察的循环

- **`custom_agent.py`** - 自定义智能体
  - 允许用户自定义智能体行为

- **`__init__.py`** - 智能体模块初始化
  - 提供 load_agent 函数用于动态加载智能体

#### 1.3 环境模块 (`environment/`)
- **`base_env.py`** - 环境基类
  - 定义了所有环境必须实现的接口
  - 继承自 gym.Env

- **具体环境实现：**
  - **`webshop_env.py`** - 网购环境
  - **`babyai_env.py`** - BabyAI 环境
  - **`jericho_env.py`** - 文本冒险游戏环境
  - **`alfworld/alfworld_env.py`** - ALFWorld 环境
  - **`scienceworld_env.py`** - 科学世界环境
  - **`pddl_env/pddl_env.py`** - PDDL 规划环境
  - **`academia_env.py`** - 学术研究环境
  - **`movie_env.py`** - 电影推荐环境
  - **`todo_env.py`** - 待办事项管理环境
  - **`weather_env.py`** - 天气查询环境
  - **`sheet_env.py`** - 电子表格操作环境

- **`browser_env/`** - 浏览器环境子模块
  - **`envs.py`** - 浏览器环境主实现
  - **`actions.py`** - 浏览器操作定义
  - **`processors.py`** - 数据处理器
  - **`utils.py`** - 工具函数

- **`WebShop/`** - WebShop 环境相关文件
  - 包含运行脚本和配置文件

#### 1.4 任务模块 (`tasks/`)
- **`base_task.py`** - 任务基类
  - 定义了评估任务的基本接口

- **具体任务实现：**
  - **`webshop.py`** - WebShop 购物任务
  - **`alfworld.py`** - ALFWorld 任务
  - **`webbrowse.py`** - 网页浏览任务
  - **`babyai.py`** - BabyAI 任务
  - **`pddl.py`** - PDDL 规划任务
  - **`scienceworld.py`** - 科学世界任务
  - **`jericho.py`** - Jericho 文本游戏任务
  - **`tool.py`** - 工具使用任务

- **`__init__.py`** - 任务模块初始化
  - 提供 load_task 函数用于动态加载任务

#### 1.5 语言模型模块 (`llm/`)
- **`openai_gpt.py`** - OpenAI GPT 模型接口
  - 支持 GPT-3.5 和 GPT-4 系列
  - 包含 token 计数和重试机制

- **`azure_gpt.py`** - Azure OpenAI 接口
- **`claude.py`** - Anthropic Claude 模型接口
- **`vllm.py`** - vLLM 本地模型接口
- **`huggingface.py`** - Hugging Face 模型接口

- **`__init__.py`** - LLM 模块初始化
  - 提供 load_llm 函数用于动态加载语言模型

### 2. 提示词模块 (`prompts/`)

#### 2.1 提示词模板
- **`prompt_template.py`** - 不同模型的提示词模板
  - 支持 deepseek, codellama, llama, vicuna, mistral 等模型
  - 定义了系统提示词和用户提示词的格式

#### 2.2 智能体特定提示词
- **`VanillaAgent/`** - 基础智能体提示词
  - 包含各种任务的提示词配置文件
  - 如：`alfworld_base.json`, `webshop_vanilla.json` 等

- **`ReactAgent/`** - ReAct智能体提示词
  - 针对 ReAct 模式优化的提示词

- **`Raw/`** - 原始提示词
  - 未经处理的基础提示词模板

### 3. 工具模块 (`utils/`)

#### 3.1 日志记录 (`logging/`)
- **`agent_logger.py`** - 智能体专用日志器
  - 支持彩色输出
  - 定义了 GOAL、FINISH 等特殊日志级别

- **`logger.py`** - 通用日志器和摘要日志器
  - TaskLogger：任务级别的日志记录
  - SummaryLogger：汇总结果的日志记录

#### 3.2 任务特定工具
- **`academia/academia_tools.py`** - 学术研究工具
  - 论文网络和作者网络分析
  - 图数据库操作功能

- **`movie/movie_tools.py`** - 电影相关工具
- **`sheet/sheets_tools.py`** - 电子表格操作工具
- **`todo/todo_tools.py`** - 待办事项管理工具
- **`weather/weather_tools.py`** - 天气查询工具

#### 3.3 通用工具 (`tool/`)
- **`data_utils.py`** - 数据处理工具
- **`helpers.py`** - 辅助函数

#### 3.4 异常处理
- **`common_exception.py`** - 自定义异常类
  - 如 PageNumberError 等

### 4. 公共模块 (`common/`)
- **`registry.py`** - 注册表系统
  - 统一管理环境、智能体、语言模型和任务的注册
  - 提供动态加载功能
  - 支持装饰器模式的组件注册

## 主要功能特性

### 1. 多任务支持
- **文本游戏**：ALFWorld, Jericho
- **网页交互**：WebShop, WebArena
- **规划任务**：PDDL
- **工具使用**：Tool-Query, Tool-Operation
- **导航任务**：BabyAI
- **科学推理**：ScienceWorld

### 2. 多智能体类型
- **VanillaAgent**：直接使用 LLM 进行推理
- **ReactAgent**：实现思考-行动-观察循环
- **CustomAgent**：支持自定义智能体逻辑

### 3. 多语言模型支持
- **OpenAI 系列**：GPT-3.5, GPT-4
- **开源模型**：通过 vLLM 和 Hugging Face 支持
- **Claude 系列**：Anthropic 的 Claude 模型

### 4. 评估指标
- **成功率**（Success Rate）
- **进度率**（Progress Rate）
- **基础准确率**（Grounding Accuracy）
- **难度分级评估**（Easy/Hard）

### 5. 可视化和日志
- **WandB 集成**：实验跟踪和可视化
- **详细日志**：每个步骤的详细记录
- **基线比较**：与基线结果的对比分析

## 使用方式

### 基本命令
```bash
python eval_main.py \
    --cfg-path config.yaml \
    --tasks webshop alfworld \
    --model gpt-4 \
    --wandb \
    --log_path ./results/
```

### 配置文件结构
配置文件包含四个主要部分：
- **llm**：语言模型配置
- **agent**：智能体配置
- **env**：环境配置
- **run**：运行配置

## 扩展性

该框架具有良好的扩展性：
1. **新任务**：继承 BaseTask 类
2. **新环境**：继承 BaseEnvironment 类
3. **新智能体**：继承 BaseAgent 类
4. **新模型**：实现相应的 LLM 接口

所有组件都通过注册表系统进行管理，支持动态加载和配置。

## 详细文件说明

### 核心文件详解

#### `eval_main.py` - 主评估脚本
这是整个框架的入口点，负责：
- 解析命令行参数（任务类型、模型、日志路径等）
- 加载配置文件（YAML格式）
- 初始化语言模型和智能体
- 执行多任务评估循环
- 记录结果到日志文件和WandB
- 支持断点续传（通过检查已有结果）

#### 智能体实现细节

**`vanilla_agent.py`**
- 最基础的智能体实现
- 直接将观察和目标传递给语言模型
- 适用于简单的问答和推理任务

**`react_agent.py`**
- 实现了ReAct（Reasoning + Acting）范式
- 包含思考（Thought）、行动（Action）、观察（Observation）循环
- 更适合需要多步推理的复杂任务

#### 环境实现细节

**`webshop_env.py`**
- 模拟在线购物环境
- 支持商品搜索、浏览、购买等操作
- 评估智能体的购物决策能力

**`browser_env/`**
- 完整的浏览器自动化环境
- 支持点击、输入、滚动等网页操作
- 用于评估网页交互能力

**`alfworld/`**
- 基于ALFWorld的家庭环境模拟
- 包含物体操作、导航等任务
- 测试智能体的空间推理能力

#### 工具系统

**`academia_tools.py`**
- 学术网络分析工具
- 支持论文引用网络和作者合作网络查询
- 包含图数据库操作功能

**各种专用工具**
- `movie_tools.py`：电影数据库查询
- `weather_tools.py`：天气信息获取
- `todo_tools.py`：任务管理功能
- `sheet_tools.py`：电子表格操作

### 配置系统

#### 注册表机制 (`registry.py`)
- 使用装饰器模式注册组件
- 支持动态加载和发现
- 统一管理所有可用的环境、智能体、模型和任务

#### 提示词管理
- 按智能体类型组织提示词
- 支持任务特定的提示词定制
- 包含多种语言模型的格式适配

### 评估指标说明

1. **成功率 (Success Rate)**：完全完成任务的比例
2. **进度率 (Progress Rate)**：任务完成的程度（0-1之间）
3. **基础准确率 (Grounding Accuracy)**：智能体行动与环境状态匹配的准确性
4. **难度分级**：区分简单和困难任务的表现

### 日志系统

#### `agent_logger.py`
- 彩色日志输出
- 特殊的日志级别（GOAL、FINISH等）
- 同时输出到控制台和文件

#### `logger.py`
- TaskLogger：记录单个任务的详细执行过程
- SummaryLogger：汇总多个任务的结果
- 支持WandB可视化集成

## 开发指南

### 添加新任务
1. 在 `tasks/` 目录创建新的任务文件
2. 继承 `BaseTask` 类
3. 实现 `evaluate()` 和 `evaluate_env()` 方法
4. 使用 `@registry.register_task("task_name")` 装饰器注册

### 添加新环境
1. 在 `environment/` 目录创建新的环境文件
2. 继承 `BaseEnvironment` 类
3. 实现所有必需的方法（step, reset, get_obs等）
4. 使用 `@registry.register_environment("env_name")` 装饰器注册

### 添加新智能体
1. 在 `agents/` 目录创建新的智能体文件
2. 继承 `BaseAgent` 类
3. 实现 `reset()`, `update()`, `run()` 方法
4. 使用 `@registry.register_agent("agent_name")` 装饰器注册

### 添加新语言模型
1. 在 `llm/` 目录创建新的模型文件
2. 实现 `generate()` 方法和 `from_config()` 类方法
3. 使用 `@registry.register_llm("model_name")` 装饰器注册

## 依赖关系

### 主要依赖
- **OpenAI**：GPT模型接口
- **Anthropic**：Claude模型接口
- **Transformers**：Hugging Face模型支持
- **Gym**：环境基类
- **WandB**：实验跟踪
- **NetworkX**：图数据处理
- **BeautifulSoup**：网页解析

### 环境特定依赖
- **ALFWorld**：家庭环境模拟
- **BabyAI**：导航任务
- **Jericho**：文本游戏
- **PDDLGym**：规划任务

## 性能优化

### 并发处理
- 支持多进程评估
- 异步环境执行
- 批量推理优化

### 缓存机制
- LLM响应缓存
- 环境状态缓存
- 结果持久化

### 内存管理
- 大型数据集的延迟加载
- 环境状态的增量更新
- 垃圾回收优化

## 文件功能速查表

| 文件/目录 | 功能描述 | 关键特性 |
|-----------|----------|----------|
| `eval_main.py` | 主评估入口 | 命令行接口、配置加载、任务调度 |
| `agents/base_agent.py` | 智能体基类 | 定义标准接口 |
| `agents/vanilla_agent.py` | 基础智能体 | 直接LLM推理 |
| `agents/react_agent.py` | ReAct智能体 | 思考-行动-观察循环 |
| `environment/base_env.py` | 环境基类 | Gym兼容接口 |
| `environment/webshop_env.py` | 购物环境 | 电商交互模拟 |
| `environment/browser_env/` | 浏览器环境 | 网页自动化 |
| `tasks/base_task.py` | 任务基类 | 评估流程定义 |
| `tasks/webshop.py` | 购物任务 | WebShop评估实现 |
| `llm/openai_gpt.py` | OpenAI接口 | GPT模型调用 |
| `llm/claude.py` | Claude接口 | Anthropic模型调用 |
| `common/registry.py` | 注册表系统 | 组件管理和动态加载 |
| `prompts/prompt_template.py` | 提示词模板 | 多模型格式适配 |
| `utils/logging/agent_logger.py` | 智能体日志 | 彩色输出、特殊级别 |
| `utils/academia/academia_tools.py` | 学术工具 | 论文网络分析 |

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export OPENAI_API_KEY="your_api_key"
export PROJECT_PATH="/path/to/agentboard"
```

### 2. 运行评估
```bash
# 基本评估
python agentboard/eval_main.py \
    --cfg-path eval_configs/main_results_all_tasks.yaml \
    --tasks webshop \
    --model gpt-4

# 多任务评估
python agentboard/eval_main.py \
    --cfg-path eval_configs/main_results_all_tasks.yaml \
    --tasks webshop alfworld babyai \
    --model gpt-4 \
    --wandb \
    --log_path ./results/
```

### 3. 查看结果
- 日志文件：`./results/logs/`
- 汇总结果：`./results/all_results.txt`
- WandB面板：在线可视化界面

## 常见问题

### Q: 如何添加自定义任务？
A: 继承 `BaseTask` 类，实现评估逻辑，并使用注册表装饰器注册。

### Q: 支持哪些语言模型？
A: OpenAI GPT系列、Claude系列、开源模型（通过vLLM/HuggingFace）。

### Q: 如何自定义评估指标？
A: 在任务类中重写评估方法，定义新的指标计算逻辑。

### Q: 能否并行运行多个任务？
A: 目前支持顺序执行，可通过修改主循环实现并行化。

---

*本文档提供了 AgentBoard 项目的完整技术说明。该框架为LLM智能体评估提供了标准化、可扩展的解决方案。*
