
(define (problem trapnewspaper) (:domain trapnewspapers)
  (:objects
        loc-0 - loc
	loc-1 - loc
	loc-10 - loc
	loc-11 - loc
	loc-12 - loc
	loc-13 - loc
	loc-14 - loc
	loc-15 - loc
	loc-16 - loc
	loc-17 - loc
	loc-18 - loc
	loc-19 - loc
	loc-2 - loc
	loc-20 - loc
	loc-21 - loc
	loc-22 - loc
	loc-23 - loc
	loc-24 - loc
	loc-25 - loc
	loc-26 - loc
	loc-27 - loc
	loc-28 - loc
	loc-29 - loc
	loc-3 - loc
	loc-30 - loc
	loc-31 - loc
	loc-32 - loc
	loc-33 - loc
	loc-4 - loc
	loc-5 - loc
	loc-6 - loc
	loc-7 - loc
	loc-8 - loc
	loc-9 - loc
	paper-0 - paper
	paper-1 - paper
	paper-10 - paper
	paper-11 - paper
	paper-12 - paper
	paper-13 - paper
	paper-14 - paper
	paper-15 - paper
	paper-16 - paper
	paper-17 - paper
	paper-18 - paper
	paper-19 - paper
	paper-2 - paper
	paper-20 - paper
	paper-21 - paper
	paper-22 - paper
	paper-23 - paper
	paper-24 - paper
	paper-25 - paper
	paper-26 - paper
	paper-27 - paper
	paper-28 - paper
	paper-29 - paper
	paper-3 - paper
	paper-30 - paper
	paper-31 - paper
	paper-32 - paper
	paper-33 - paper
	paper-34 - paper
	paper-35 - paper
	paper-36 - paper
	paper-37 - paper
	paper-38 - paper
	paper-39 - paper
	paper-4 - paper
	paper-40 - paper
	paper-41 - paper
	paper-5 - paper
	paper-6 - paper
	paper-7 - paper
	paper-8 - paper
	paper-9 - paper
  )
  (:init 
	(at loc-0)
	(ishomebase loc-0)
	(safe loc-0)
	(safe loc-10)
	(safe loc-11)
	(safe loc-12)
	(safe loc-13)
	(safe loc-16)
	(safe loc-17)
	(safe loc-19)
	(safe loc-1)
	(safe loc-21)
	(safe loc-28)
	(safe loc-29)
	(safe loc-2)
	(safe loc-30)
	(safe loc-31)
	(safe loc-33)
	(safe loc-4)
	(safe loc-5)
	(safe loc-6)
	(safe loc-8)
	(unpacked paper-0)
	(unpacked paper-10)
	(unpacked paper-11)
	(unpacked paper-12)
	(unpacked paper-13)
	(unpacked paper-14)
	(unpacked paper-15)
	(unpacked paper-16)
	(unpacked paper-17)
	(unpacked paper-18)
	(unpacked paper-19)
	(unpacked paper-1)
	(unpacked paper-20)
	(unpacked paper-21)
	(unpacked paper-22)
	(unpacked paper-23)
	(unpacked paper-24)
	(unpacked paper-25)
	(unpacked paper-26)
	(unpacked paper-27)
	(unpacked paper-28)
	(unpacked paper-29)
	(unpacked paper-2)
	(unpacked paper-30)
	(unpacked paper-31)
	(unpacked paper-32)
	(unpacked paper-33)
	(unpacked paper-34)
	(unpacked paper-35)
	(unpacked paper-36)
	(unpacked paper-37)
	(unpacked paper-38)
	(unpacked paper-39)
	(unpacked paper-3)
	(unpacked paper-40)
	(unpacked paper-41)
	(unpacked paper-4)
	(unpacked paper-5)
	(unpacked paper-6)
	(unpacked paper-7)
	(unpacked paper-8)
	(unpacked paper-9)
	(wantspaper loc-10)
	(wantspaper loc-11)
	(wantspaper loc-12)
	(wantspaper loc-13)
	(wantspaper loc-16)
	(wantspaper loc-17)
	(wantspaper loc-19)
	(wantspaper loc-1)
	(wantspaper loc-21)
	(wantspaper loc-28)
	(wantspaper loc-29)
	(wantspaper loc-2)
	(wantspaper loc-30)
	(wantspaper loc-31)
	(wantspaper loc-33)
	(wantspaper loc-4)
	(wantspaper loc-5)
	(wantspaper loc-6)
	(wantspaper loc-8)
  )
  (:goal (and
	(satisfied loc-1)
	(satisfied loc-2)
	(satisfied loc-4)
	(satisfied loc-5)
	(satisfied loc-6)
	(satisfied loc-8)
	(satisfied loc-10)
	(satisfied loc-11)
	(satisfied loc-12)
	(satisfied loc-13)
	(satisfied loc-16)
	(satisfied loc-17)
	(satisfied loc-19)
	(satisfied loc-21)
	(satisfied loc-28)
	(satisfied loc-29)
	(satisfied loc-30)
	(satisfied loc-31)
	(satisfied loc-33)))
)
