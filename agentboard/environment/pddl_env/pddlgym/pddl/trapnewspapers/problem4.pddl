
(define (problem trapnewspaper) (:domain trapnewspapers)
  (:objects
        loc-0 - loc
	loc-1 - loc
	loc-2 - loc
	loc-3 - loc
	loc-4 - loc
	loc-5 - loc
	loc-6 - loc
	paper-0 - paper
	paper-1 - paper
	paper-10 - paper
	paper-2 - paper
	paper-3 - paper
	paper-4 - paper
	paper-5 - paper
	paper-6 - paper
	paper-7 - paper
	paper-8 - paper
	paper-9 - paper
  )
  (:init 
	(at loc-0)
	(ishomebase loc-0)
	(safe loc-0)
	(safe loc-2)
	(safe loc-3)
	(safe loc-4)
	(safe loc-5)
	(safe loc-6)
	(unpacked paper-0)
	(unpacked paper-10)
	(unpacked paper-1)
	(unpacked paper-2)
	(unpacked paper-3)
	(unpacked paper-4)
	(unpacked paper-5)
	(unpacked paper-6)
	(unpacked paper-7)
	(unpacked paper-8)
	(unpacked paper-9)
	(wantspaper loc-2)
	(wantspaper loc-3)
	(wantspaper loc-4)
	(wantspaper loc-5)
	(wantspaper loc-6)
  )
  (:goal (and
	(satisfied loc-2)
	(satisfied loc-3)
	(satisfied loc-4)
	(satisfied loc-5)
	(satisfied loc-6)))
)
