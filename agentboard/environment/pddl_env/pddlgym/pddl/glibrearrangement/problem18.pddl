(define (problem rearrangement) 
    (:domain glibrearrangement)

    (:objects
    
	pawn-0 - moveable
	pawn-1 - moveable
	pawn-2 - moveable
	monkey-3 - moveable
	robot - moveable
	loc-0-0 - static
	loc-0-1 - static
	loc-0-2 - static
	loc-1-0 - static
	loc-1-1 - static
	loc-1-2 - static
	loc-2-0 - static
	loc-2-1 - static
	loc-2-2 - static
    )

    (:init
    
	(ispawn pawn-0)
	(ispawn pawn-1)
	(ispawn pawn-2)
	(ismonkey monkey-3)
	(isrobot robot)
	(at pawn-0 loc-0-0)
	(at pawn-1 loc-1-2)
	(at pawn-2 loc-0-2)
	(at monkey-3 loc-1-1)
	(at robot loc-0-0)
	(handsfree robot)

    ; action literals
    
	(pick pawn-0)
	(place pawn-0)
	(pick pawn-1)
	(place pawn-1)
	(pick pawn-2)
	(place pawn-2)
	(pick monkey-3)
	(place monkey-3)
	(moveto loc-0-0)
	(moveto loc-0-1)
	(moveto loc-0-2)
	(moveto loc-1-0)
	(moveto loc-1-1)
	(moveto loc-1-2)
	(moveto loc-2-0)
	(moveto loc-2-1)
	(moveto loc-2-2)
    )

    (:goal (and  (at pawn-0 loc-2-1) ))
)
    