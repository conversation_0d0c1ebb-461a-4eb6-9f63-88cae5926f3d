
(define (problem generatedblocks) (:domain blocks)
  (:objects
        b0 - block
	b1 - block
	b10 - block
	b11 - block
	b12 - block
	b13 - block
	b14 - block
	b15 - block
	b16 - block
	b17 - block
	b18 - block
	b19 - block
	b2 - block
	b20 - block
	b21 - block
	b22 - block
	b3 - block
	b4 - block
	b5 - block
	b6 - block
	b7 - block
	b8 - block
	b9 - block
  )
  (:init 
	(clear b0)
	(clear b12)
	(clear b15)
	(clear b19)
	(clear b3)
	(clear b7)
	(handempty)
	(on b0 b1)
	(on b10 b11)
	(on b12 b13)
	(on b13 b14)
	(on b15 b16)
	(on b16 b17)
	(on b17 b18)
	(on b19 b20)
	(on b1 b2)
	(on b20 b21)
	(on b21 b22)
	(on b3 b4)
	(on b4 b5)
	(on b5 b6)
	(on b7 b8)
	(on b8 b9)
	(on b9 b10)
	(ontable b11)
	(ontable b14)
	(ontable b18)
	(ontable b22)
	(ontable b2)
	(ontable b6)
  )
  (:goal (and
	(on b21 b6)
	(on b6 b3)
	(on b3 b15)
	(ontable b15)
	(on b5 b20)
	(on b20 b8)
	(on b8 b4)
	(ontable b4)
	(on b22 b2)
	(on b2 b1)
	(on b1 b17)
	(on b17 b10)
	(ontable b10)
	(on b7 b9)
	(on b9 b11)
	(on b11 b0)
	(ontable b0)
	(on b14 b12)
	(on b12 b16)
	(on b16 b13)
	(ontable b13)))
)
