
(define (problem newspaper) (:domain newspapers)
  (:objects
        loc-0 - loc
	loc-1 - loc
	loc-10 - loc
	loc-11 - loc
	loc-12 - loc
	loc-13 - loc
	loc-2 - loc
	loc-3 - loc
	loc-4 - loc
	loc-5 - loc
	loc-6 - loc
	loc-7 - loc
	loc-8 - loc
	loc-9 - loc
	paper-0 - paper
	paper-1 - paper
	paper-10 - paper
	paper-11 - paper
	paper-12 - paper
	paper-13 - paper
	paper-14 - paper
	paper-15 - paper
	paper-16 - paper
	paper-17 - paper
	paper-18 - paper
	paper-19 - paper
	paper-2 - paper
	paper-20 - paper
	paper-3 - paper
	paper-4 - paper
	paper-5 - paper
	paper-6 - paper
	paper-7 - paper
	paper-8 - paper
	paper-9 - paper
  )
  (:init 
	(at loc-0)
	(ishomebase loc-0)
	(unpacked paper-0)
	(unpacked paper-10)
	(unpacked paper-11)
	(unpacked paper-12)
	(unpacked paper-13)
	(unpacked paper-14)
	(unpacked paper-15)
	(unpacked paper-16)
	(unpacked paper-17)
	(unpacked paper-18)
	(unpacked paper-19)
	(unpacked paper-1)
	(unpacked paper-20)
	(unpacked paper-2)
	(unpacked paper-3)
	(unpacked paper-4)
	(unpacked paper-5)
	(unpacked paper-6)
	(unpacked paper-7)
	(unpacked paper-8)
	(unpacked paper-9)
	(wantspaper loc-10)
	(wantspaper loc-11)
	(wantspaper loc-12)
	(wantspaper loc-13)
	(wantspaper loc-1)
	(wantspaper loc-2)
	(wantspaper loc-3)
	(wantspaper loc-4)
	(wantspaper loc-5)
	(wantspaper loc-6)
	(wantspaper loc-7)
	(wantspaper loc-8)
	(wantspaper loc-9)
  )
  (:goal (and
	(satisfied loc-1)
	(satisfied loc-2)
	(satisfied loc-3)
	(satisfied loc-4)
	(satisfied loc-5)
	(satisfied loc-6)
	(satisfied loc-7)
	(satisfied loc-8)
	(satisfied loc-9)
	(satisfied loc-10)
	(satisfied loc-11)
	(satisfied loc-12)
	(satisfied loc-13)))
)
