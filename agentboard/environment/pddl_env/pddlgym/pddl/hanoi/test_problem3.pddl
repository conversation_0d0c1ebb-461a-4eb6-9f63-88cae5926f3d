(define (problem testhanoi3)
  (:domain hanoi)
  (:objects peg1 peg2 peg3 d1 d2 d3 d4 d5 d6 d7 d8 d9 d10)
  (:init
   (smaller peg1 d1) (smaller peg1 d2) (smaller peg1 d3) (smaller peg1 d4)
   (smaller peg1 d5) (smaller peg1 d6) (smaller peg1 d7) (smaller peg1 d8)
   (smaller peg1 d9) (smaller peg1 d10)
   (smaller peg2 d1) (smaller peg2 d2) (smaller peg2 d3) (smaller peg2 d4)
   (smaller peg2 d5) (smaller peg2 d6) (smaller peg2 d7) (smaller peg2 d8)
   (smaller peg2 d9) (smaller peg2 d10)
   (smaller peg3 d1) (smaller peg3 d2) (smaller peg3 d3) (smaller peg3 d4)
   (smaller peg3 d5) (smaller peg3 d6) (smaller peg3 d7) (smaller peg3 d8)
   (smaller peg3 d9) (smaller peg3 d10)
   (smaller d2 d1) (smaller d3 d1) (smaller d3 d2) (smaller d4 d1)
   (smaller d4 d2) (smaller d4 d3) (smaller d5 d1) (smaller d5 d2)
   (smaller d5 d3) (smaller d5 d4) (smaller d6 d1) (smaller d6 d2)
   (smaller d6 d3) (smaller d6 d4) (smaller d6 d5) (smaller d7 d1)
   (smaller d7 d2) (smaller d7 d3) (smaller d7 d4) (smaller d7 d5)
   (smaller d7 d6) (smaller d8 d1) (smaller d8 d2) (smaller d8 d3)
   (smaller d8 d4) (smaller d8 d5) (smaller d8 d6) (smaller d8 d7)
   (smaller d9 d1) (smaller d9 d2) (smaller d9 d3) (smaller d9 d4)
   (smaller d9 d5) (smaller d9 d6) (smaller d9 d7) (smaller d9 d8)
   (smaller d10 d1) (smaller d10 d2) (smaller d10 d3) (smaller d10 d4)
   (smaller d10 d5) (smaller d10 d6) (smaller d10 d7) (smaller d10 d8)
   (smaller d10 d9)
   (clear peg2) (clear peg3) (clear d1)
   (on d10 peg1) (on d9 d10) (on d8 d9) (on d7 d8) (on d6 d7) (on d5 d6) (on d4 d5) (on d3 d4)
   (on d2 d3) (on d1 d2)
   (move d1 d2)
   (move d1 d3)
   (move d1 d4)
   (move d1 d5)
   (move d1 d6)
   (move d1 d7)
   (move d1 d8)
   (move d1 d9)
   (move d1 d10)
   (move d1 peg1)
   (move d1 peg2)
   (move d1 peg3)
   (move d2 d1)
   (move d2 d3)
   (move d2 d4)
   (move d2 d5)
   (move d2 d6)
   (move d2 d7)
   (move d2 d8)
   (move d2 d9)
   (move d2 d10)
   (move d2 peg1)
   (move d2 peg2)
   (move d2 peg3)
   (move d3 d1)
   (move d3 d2)
   (move d3 d4)
   (move d3 d5)
   (move d3 d6)
   (move d3 d7)
   (move d3 d8)
   (move d3 d9)
   (move d3 d10)
   (move d3 peg1)
   (move d3 peg2)
   (move d3 peg3)
   (move d4 d1)
   (move d4 d2)
   (move d4 d3)
   (move d4 d5)
   (move d4 d6)
   (move d4 d7)
   (move d4 d8)
   (move d4 d9)
   (move d4 d10)
   (move d4 peg1)
   (move d4 peg2)
   (move d4 peg3)
   (move d5 d1)
   (move d5 d2)
   (move d5 d3)
   (move d5 d4)
   (move d5 d6)
   (move d5 d7)
   (move d5 d8)
   (move d5 d9)
   (move d5 d10)
   (move d5 peg1)
   (move d5 peg2)
   (move d5 peg3)
   (move d6 d1)
   (move d6 d2)
   (move d6 d3)
   (move d6 d4)
   (move d6 d5)
   (move d6 d7)
   (move d6 d8)
   (move d6 d9)
   (move d6 d10)
   (move d6 peg1)
   (move d6 peg2)
   (move d6 peg3)
   (move d7 d1)
   (move d7 d2)
   (move d7 d3)
   (move d7 d4)
   (move d7 d5)
   (move d7 d6)
   (move d7 d8)
   (move d7 d9)
   (move d7 d10)
   (move d7 peg1)
   (move d7 peg2)
   (move d7 peg3)
   (move d8 d1)
   (move d8 d2)
   (move d8 d3)
   (move d8 d4)
   (move d8 d5)
   (move d8 d6)
   (move d8 d7)
   (move d8 d9)
   (move d8 d10)
   (move d8 peg1)
   (move d8 peg2)
   (move d8 peg3)
   (move d9 d1)
   (move d9 d2)
   (move d9 d3)
   (move d9 d4)
   (move d9 d5)
   (move d9 d6)
   (move d9 d7)
   (move d9 d8)
   (move d9 d10)
   (move d9 peg1)
   (move d9 peg2)
   (move d9 peg3)
   (move d10 d1)
   (move d10 d2)
   (move d10 d3)
   (move d10 d4)
   (move d10 d5)
   (move d10 d6)
   (move d10 d7)
   (move d10 d8)
   (move d10 d9)
   (move d10 peg1)
   (move d10 peg2)
   (move d10 peg3)
  )
  (:goal (and (on d10 peg3) (on d9 d10) (on d8 d9) (on d7 d8) (on d6 d7) (on d5 d6) (on d4 d5)
	      (on d3 d4) (on d2 d3) (on d1 d2)))
  )