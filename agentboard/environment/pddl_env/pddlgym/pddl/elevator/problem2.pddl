
(define (problem mixed-f6-p3-u0-v0-g0-a0-n0-a0-b0-n0-f0-r0) (:domain miconic)
  (:objects
        f0 - floor
	f1 - floor
	f2 - floor
	f3 - floor
	f4 - floor
	f5 - floor
	p0 - passenger
	p1 - passenger
	p2 - passenger
  )
  (:goal (and
	(served p0)
	(served p1)
	(served p2)))
  (:init 
	(above f0 f1)
	(above f0 f2)
	(above f0 f3)
	(above f0 f4)
	(above f0 f5)
	(above f1 f2)
	(above f1 f3)
	(above f1 f4)
	(above f1 f5)
	(above f2 f3)
	(above f2 f4)
	(above f2 f5)
	(above f3 f4)
	(above f3 f5)
	(above f4 f5)
	(board f0 p0)
	(board f0 p1)
	(board f0 p2)
	(board f1 p0)
	(board f1 p1)
	(board f1 p2)
	(board f2 p0)
	(board f2 p1)
	(board f2 p2)
	(board f3 p0)
	(board f3 p1)
	(board f3 p2)
	(board f4 p0)
	(board f4 p1)
	(board f4 p2)
	(board f5 p0)
	(board f5 p1)
	(board f5 p2)
	(depart f0 p0)
	(depart f0 p1)
	(depart f0 p2)
	(depart f1 p0)
	(depart f1 p1)
	(depart f1 p2)
	(depart f2 p0)
	(depart f2 p1)
	(depart f2 p2)
	(depart f3 p0)
	(depart f3 p1)
	(depart f3 p2)
	(depart f4 p0)
	(depart f4 p1)
	(depart f4 p2)
	(depart f5 p0)
	(depart f5 p1)
	(depart f5 p2)
	(destin p0 f4)
	(destin p1 f1)
	(destin p2 f1)
	(down f0)
	(down f1)
	(down f2)
	(down f3)
	(down f4)
	(down f5)
	(lift-at f0)
	(origin p0 f1)
	(origin p1 f3)
	(origin p2 f5)
	(up f0)
	(up f1)
	(up f2)
	(up f3)
	(up f4)
	(up f5)
))
        