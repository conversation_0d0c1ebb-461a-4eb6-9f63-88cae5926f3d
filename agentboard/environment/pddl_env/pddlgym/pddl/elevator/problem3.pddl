
(define (problem mixed-f8-p4-u0-v0-g0-a0-n0-a0-b0-n0-f0-r0) (:domain miconic)
  (:objects
        f0 - floor
	f1 - floor
	f2 - floor
	f3 - floor
	f4 - floor
	f5 - floor
	f6 - floor
	f7 - floor
	p0 - passenger
	p1 - passenger
	p2 - passenger
	p3 - passenger
  )
  (:goal (and
	(served p0)
	(served p1)
	(served p2)
	(served p3)))
  (:init 
	(above f0 f1)
	(above f0 f2)
	(above f0 f3)
	(above f0 f4)
	(above f0 f5)
	(above f0 f6)
	(above f0 f7)
	(above f1 f2)
	(above f1 f3)
	(above f1 f4)
	(above f1 f5)
	(above f1 f6)
	(above f1 f7)
	(above f2 f3)
	(above f2 f4)
	(above f2 f5)
	(above f2 f6)
	(above f2 f7)
	(above f3 f4)
	(above f3 f5)
	(above f3 f6)
	(above f3 f7)
	(above f4 f5)
	(above f4 f6)
	(above f4 f7)
	(above f5 f6)
	(above f5 f7)
	(above f6 f7)
	(board f0 p0)
	(board f0 p1)
	(board f0 p2)
	(board f0 p3)
	(board f1 p0)
	(board f1 p1)
	(board f1 p2)
	(board f1 p3)
	(board f2 p0)
	(board f2 p1)
	(board f2 p2)
	(board f2 p3)
	(board f3 p0)
	(board f3 p1)
	(board f3 p2)
	(board f3 p3)
	(board f4 p0)
	(board f4 p1)
	(board f4 p2)
	(board f4 p3)
	(board f5 p0)
	(board f5 p1)
	(board f5 p2)
	(board f5 p3)
	(board f6 p0)
	(board f6 p1)
	(board f6 p2)
	(board f6 p3)
	(board f7 p0)
	(board f7 p1)
	(board f7 p2)
	(board f7 p3)
	(depart f0 p0)
	(depart f0 p1)
	(depart f0 p2)
	(depart f0 p3)
	(depart f1 p0)
	(depart f1 p1)
	(depart f1 p2)
	(depart f1 p3)
	(depart f2 p0)
	(depart f2 p1)
	(depart f2 p2)
	(depart f2 p3)
	(depart f3 p0)
	(depart f3 p1)
	(depart f3 p2)
	(depart f3 p3)
	(depart f4 p0)
	(depart f4 p1)
	(depart f4 p2)
	(depart f4 p3)
	(depart f5 p0)
	(depart f5 p1)
	(depart f5 p2)
	(depart f5 p3)
	(depart f6 p0)
	(depart f6 p1)
	(depart f6 p2)
	(depart f6 p3)
	(depart f7 p0)
	(depart f7 p1)
	(depart f7 p2)
	(depart f7 p3)
	(destin p0 f6)
	(destin p1 f3)
	(destin p2 f7)
	(destin p3 f4)
	(down f0)
	(down f1)
	(down f2)
	(down f3)
	(down f4)
	(down f5)
	(down f6)
	(down f7)
	(lift-at f0)
	(origin p0 f7)
	(origin p1 f1)
	(origin p2 f1)
	(origin p3 f2)
	(up f0)
	(up f1)
	(up f2)
	(up f3)
	(up f4)
	(up f5)
	(up f6)
	(up f7)
))
        