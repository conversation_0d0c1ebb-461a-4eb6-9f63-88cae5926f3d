(define (domain rover)

 (:predicates (at_rover0_waypoint0) (at_rover0_waypoint3)
 (at_rover0_waypoint1) (at_rover0_waypoint2)
 (at_soil_sample_waypoint0) (empty_rover0store) (full_rover0store)
 (have_soil_analysis_rover0_waypoint0) (at_soil_sample_waypoint2)
 (have_soil_analysis_rover0_waypoint2) (at_soil_sample_waypoint3)
 (have_soil_analysis_rover0_waypoint3) (at_rock_sample_waypoint1)
 (have_rock_analysis_rover0_waypoint1) (at_rock_sample_waypoint2)
 (have_rock_analysis_rover0_waypoint2) (at_rock_sample_waypoint3)
 (have_rock_analysis_rover0_waypoint3) (calibrated_camera0_rover0)
 (have_image_rover0_objective0_colour)
 (have_image_rover0_objective0_high_res)
 (have_image_rover0_objective1_colour)
 (have_image_rover0_objective1_high_res)
 (communicated_soil_data_waypoint0) (communicated_soil_data_waypoint2)
 (communicated_soil_data_waypoint3) (communicated_rock_data_waypoint1)
 (communicated_rock_data_waypoint2) (communicated_rock_data_waypoint3)
 (communicated_image_data_objective0_colour)
 (communicated_image_data_objective0_high_res)
 (communicated_image_data_objective1_colour)
 (communicated_image_data_objective1_high_res) (ok_a0) (ok_a1) (ok_e0)
 (ok_e1) (ok_e2) (ok_o0) (once_o0) (negof_once_o0) (ok_o1) (once_o1)
 (negof_once_o1) (ok_o2) (once_o2) (negof_once_o2) (ok_o3) (once_o3)
 (negof_once_o3) (safe_sb3) (ok_sb3) (safe_sb7) (ok_sb7) (safe_sb8)
 (ok_sb8) (safe_sb11) (ok_sb11) (safe_sb12) (ok_sb12) (safe_sb13)
 (ok_sb13) (safe_sb16) (ok_sb16) (safe_sb17) (ok_sb17) (safe_sb19)
 (ok_sb19) (safe_sb20) (ok_sb20) (normal-mode) (a0) (a1) (e0) (e1)
 (e2) (o0) (o1) (o2) (o3) (sb3) (sb7) (sb8) (sb11) (sb12) (sb13)
 (sb16) (sb17) (sb19) (sb20))

 (:functions (total-cost)) 

 (:action copy_0_navigate_rover0_waypoint1_waypoint2 :parameters ()
 :precondition (and (at_rover0_waypoint1) (safe_sb3) (normal-mode))
 :effect (and (at_rover0_waypoint2) (once_o1) (safe_sb12) (not
 (at_rover0_waypoint1)) (not (negof_once_o1)) (increase (total-cost)
 0)) )

 (:action copy_0_navigate_rover0_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (normal-mode))
  :effect (and (at_rover0_waypoint0) (ok_e0) (once_o0) (safe_sb11) (not (at_rover0_waypoint3)) (not (negof_once_o0)) (increase (total-cost) 0))
 )
 (:action copy_0_navigate_rover0_waypoint3_waypoint1
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (negof_once_o1) (normal-mode))
  :effect (and (at_rover0_waypoint1) (once_o0) (not (at_rover0_waypoint3)) (not (negof_once_o0)) (increase (total-cost) 0))
 )
 (:action copy_0_sample_soil_rover0_rover0store_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (at_soil_sample_waypoint0) (empty_rover0store) (negof_once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint0) (ok_e1) (once_o2) (safe_sb3) (safe_sb7) (safe_sb8) (safe_sb13) (safe_sb19) (not (at_soil_sample_waypoint0)) (not (empty_rover0store)) (not (ok_a0)) (not (negof_once_o2)) (increase (total-cost) 0))
 )
 (:action copy_0_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (negof_once_o3) (safe_sb7) (safe_sb8) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (increase (total-cost) 0))
 )
 (:action copy_0_sample_soil_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_soil_sample_waypoint3) (negof_once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint3)) (not (negof_once_o2)) (increase (total-cost) 0))
 )
 (:action copy_0_sample_rock_rover0_rover0store_waypoint1
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (empty_rover0store) (at_rock_sample_waypoint1) (negof_once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint1) (ok_e2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb16) (safe_sb19) (not (empty_rover0store)) (not (at_rock_sample_waypoint1)) (not (ok_a1)) (not (negof_once_o2)) (increase (total-cost) 0))
 )
 (:action copy_0_sample_rock_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_rock_sample_waypoint2) (negof_once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_rock_sample_waypoint2)) (not (negof_once_o2)) (increase (total-cost) 0))
 )
 (:action copy_0_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (increase (total-cost) 0))
 )
 (:action copy_0_calibrate_rover0_camera0_objective1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (normal-mode))
  :effect (and (calibrated_camera0_rover0) (increase (total-cost) 0))
 )
 (:action copy_0_calibrate_rover0_camera0_objective1_waypoint1
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (normal-mode))
  :effect (and (calibrated_camera0_rover0) (increase (total-cost) 0))
 )
 (:action copy_0_calibrate_rover0_camera0_objective1_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (normal-mode))
  :effect (and (calibrated_camera0_rover0) (increase (total-cost) 0))
 )
 (:action copy_0_calibrate_rover0_camera0_objective1_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (normal-mode))
  :effect (and (calibrated_camera0_rover0) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint0_objective0_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint0_objective0_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_high_res) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint0_objective1_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint0_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (safe_sb19) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint1_objective0_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint1_objective0_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_high_res) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint1_objective1_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint1_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (safe_sb19) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint2_objective0_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint2_objective0_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_high_res) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint2_objective1_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint2_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (safe_sb19) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint3_objective0_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint3_objective0_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective0_high_res) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint3_objective1_camera0_colour
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_colour) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_take_image_rover0_waypoint3_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (safe_sb19) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint0_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_soil_analysis_rover0_waypoint0) (normal-mode))
  :effect (and (communicated_soil_data_waypoint0) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint0_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_soil_analysis_rover0_waypoint0) (normal-mode))
  :effect (and (communicated_soil_data_waypoint0) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint0_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_soil_analysis_rover0_waypoint0) (normal-mode))
  :effect (and (communicated_soil_data_waypoint0) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint2_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_soil_analysis_rover0_waypoint2) (normal-mode))
  :effect (and (communicated_soil_data_waypoint2) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint2_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_soil_analysis_rover0_waypoint2) (normal-mode))
  :effect (and (communicated_soil_data_waypoint2) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint2_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_soil_analysis_rover0_waypoint2) (normal-mode))
  :effect (and (communicated_soil_data_waypoint2) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint3_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_soil_analysis_rover0_waypoint3) (normal-mode))
  :effect (and (communicated_soil_data_waypoint3) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint3_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_soil_analysis_rover0_waypoint3) (normal-mode))
  :effect (and (communicated_soil_data_waypoint3) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_soil_data_rover0_general_waypoint3_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_soil_analysis_rover0_waypoint3) (normal-mode))
  :effect (and (communicated_soil_data_waypoint3) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint1_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_rock_analysis_rover0_waypoint1) (normal-mode))
  :effect (and (communicated_rock_data_waypoint1) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint1_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_rock_analysis_rover0_waypoint1) (normal-mode))
  :effect (and (communicated_rock_data_waypoint1) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint1_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_rock_analysis_rover0_waypoint1) (normal-mode))
  :effect (and (communicated_rock_data_waypoint1) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint2_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_rock_analysis_rover0_waypoint2) (normal-mode))
  :effect (and (communicated_rock_data_waypoint2) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint2_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_rock_analysis_rover0_waypoint2) (normal-mode))
  :effect (and (communicated_rock_data_waypoint2) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint2_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_rock_analysis_rover0_waypoint2) (normal-mode))
  :effect (and (communicated_rock_data_waypoint2) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint3_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_rock_analysis_rover0_waypoint3) (normal-mode))
  :effect (and (communicated_rock_data_waypoint3) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint3_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_rock_analysis_rover0_waypoint3) (normal-mode))
  :effect (and (communicated_rock_data_waypoint3) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_rock_data_rover0_general_waypoint3_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_rock_analysis_rover0_waypoint3) (normal-mode))
  :effect (and (communicated_rock_data_waypoint3) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective0_colour_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_image_rover0_objective0_colour) (normal-mode))
  :effect (and (communicated_image_data_objective0_colour) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective0_colour_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_image_rover0_objective0_colour) (normal-mode))
  :effect (and (communicated_image_data_objective0_colour) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective0_colour_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_image_rover0_objective0_colour) (normal-mode))
  :effect (and (communicated_image_data_objective0_colour) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective0_high_res_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_image_rover0_objective0_high_res) (normal-mode))
  :effect (and (communicated_image_data_objective0_high_res) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective0_high_res_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_image_rover0_objective0_high_res) (normal-mode))
  :effect (and (communicated_image_data_objective0_high_res) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective0_high_res_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_image_rover0_objective0_high_res) (normal-mode))
  :effect (and (communicated_image_data_objective0_high_res) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective1_colour_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_image_rover0_objective1_colour) (normal-mode))
  :effect (and (communicated_image_data_objective1_colour) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective1_colour_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_image_rover0_objective1_colour) (normal-mode))
  :effect (and (communicated_image_data_objective1_colour) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective1_colour_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_image_rover0_objective1_colour) (normal-mode))
  :effect (and (communicated_image_data_objective1_colour) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective1_high_res_waypoint1_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (have_image_rover0_objective1_high_res) (normal-mode))
  :effect (and (communicated_image_data_objective1_high_res) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective1_high_res_waypoint2_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (have_image_rover0_objective1_high_res) (normal-mode))
  :effect (and (communicated_image_data_objective1_high_res) (increase (total-cost) 0))
 )
 (:action copy_0_communicate_image_data_rover0_general_objective1_high_res_waypoint3_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (have_image_rover0_objective1_high_res) (normal-mode))
  :effect (and (communicated_image_data_objective1_high_res) (increase (total-cost) 0))
 )
 (:action copy_0_navigate_rover0_waypoint0_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (once_o0) (normal-mode))
  :effect (and (at_rover0_waypoint3) (not (at_rover0_waypoint0)) (not (ok_o0)) (increase (total-cost) 0))
 )
 (:action copy_0_navigate_rover0_waypoint1_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (once_o0) (normal-mode))
  :effect (and (at_rover0_waypoint3) (once_o1) (not (at_rover0_waypoint1)) (not (ok_o0)) (not (negof_once_o1)) (increase (total-cost) 0))
 )
 (:action copy_0_navigate_rover0_waypoint2_waypoint1
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (once_o1) (normal-mode))
  :effect (and (at_rover0_waypoint1) (not (at_rover0_waypoint2)) (not (ok_o1)) (increase (total-cost) 0))
 )
 (:action copy_1_navigate_rover0_waypoint3_waypoint1
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (once_o1) (normal-mode))
  :effect (and (at_rover0_waypoint1) (once_o0) (not (at_rover0_waypoint3)) (not (negof_once_o0)) (not (ok_o1)) (increase (total-cost) 0))
 )
 (:action copy_0_drop_rover0_rover0store
  :parameters ()
  :precondition (and (full_rover0store) (once_o2) (normal-mode))
  :effect (and (empty_rover0store) (once_o3) (not (full_rover0store)) (not (ok_o2)) (not (negof_once_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_sample_soil_rover0_rover0store_waypoint0
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (at_soil_sample_waypoint0) (empty_rover0store) (once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint0) (ok_e1) (once_o2) (safe_sb3) (safe_sb7) (safe_sb8) (safe_sb13) (safe_sb19) (not (at_soil_sample_waypoint0)) (not (empty_rover0store)) (not (ok_a0)) (not (negof_once_o2)) (not (ok_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (once_o3) (safe_sb7) (safe_sb8) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_sample_soil_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_soil_sample_waypoint3) (once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_sample_rock_rover0_rover0store_waypoint1
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (empty_rover0store) (at_rock_sample_waypoint1) (once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint1) (ok_e2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb16) (safe_sb19) (not (empty_rover0store)) (not (at_rock_sample_waypoint1)) (not (ok_a1)) (not (negof_once_o2)) (not (ok_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_sample_rock_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_rock_sample_waypoint2) (once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_rock_sample_waypoint2)) (not (negof_once_o2)) (not (ok_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (increase (total-cost) 0))
 )
 (:action copy_1_navigate_rover0_waypoint1_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (normal-mode))
  :effect (and (at_rover0_waypoint2) (once_o1) (safe_sb12) (not (at_rover0_waypoint1)) (not (negof_once_o1)) (not (ok_sb3)) (increase (total-cost) 0))
 )
 (:action copy_2_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (negof_once_o3) (safe_sb8) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_sb7)) (increase (total-cost) 0))
 )
 (:action copy_3_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (once_o3) (safe_sb8) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb7)) (increase (total-cost) 0))
 )
 (:action copy_4_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (negof_once_o3) (safe_sb7) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_sb8)) (increase (total-cost) 0))
 )
 (:action copy_5_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (once_o3) (safe_sb7) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb8)) (increase (total-cost) 0))
 )
 (:action copy_6_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (negof_once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_sb7)) (not (ok_sb8)) (increase (total-cost) 0))
 )
 (:action copy_7_sample_soil_rover0_rover0store_waypoint2
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (empty_rover0store) (at_soil_sample_waypoint2) (once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_soil_analysis_rover0_waypoint2) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (not (empty_rover0store)) (not (at_soil_sample_waypoint2)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb7)) (not (ok_sb8)) (increase (total-cost) 0))
 )
 (:action copy_2_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (increase (total-cost) 0))
 )
 (:action copy_3_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (increase (total-cost) 0))
 )
 (:action copy_4_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (increase (total-cost) 0))
 )
 (:action copy_5_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (increase (total-cost) 0))
 )
 (:action copy_6_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (increase (total-cost) 0))
 )
 (:action copy_7_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb13) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (increase (total-cost) 0))
 )
 (:action copy_8_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_9_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_10_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_11_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_12_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_13_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_14_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_15_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb16) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (increase (total-cost) 0))
 )
 (:action copy_16_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_17_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_18_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_19_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_20_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_21_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_22_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_23_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb13) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_24_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_25_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_26_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_27_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_28_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_29_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_30_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_31_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb17) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (increase (total-cost) 0))
 )
 (:action copy_32_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_33_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_34_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_35_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_36_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_37_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_38_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_39_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb13) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_40_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_41_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_42_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_43_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_44_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_45_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_46_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_47_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb16) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_48_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_49_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_50_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_51_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_52_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_53_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_54_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_55_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb13) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_56_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (safe_sb12) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_57_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (safe_sb12) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_58_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb12) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_59_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb12) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_60_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (safe_sb11) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_61_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (safe_sb11) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_62_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (negof_once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_63_sample_rock_rover0_rover0store_waypoint3
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (empty_rover0store) (at_rock_sample_waypoint3) (once_o3) (normal-mode))
  :effect (and (full_rover0store) (have_rock_analysis_rover0_waypoint3) (once_o2) (safe_sb7) (safe_sb13) (safe_sb19) (safe_sb20) (not (empty_rover0store)) (not (at_rock_sample_waypoint3)) (not (negof_once_o2)) (not (ok_o3)) (not (ok_sb11)) (not (ok_sb12)) (not (ok_sb13)) (not (ok_sb16)) (not (ok_sb17)) (increase (total-cost) 0))
 )
 (:action copy_1_take_image_rover0_waypoint0_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (increase (total-cost) 0))
 )
 (:action copy_1_take_image_rover0_waypoint1_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (increase (total-cost) 0))
 )
 (:action copy_1_take_image_rover0_waypoint2_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (increase (total-cost) 0))
 )
 (:action copy_1_take_image_rover0_waypoint3_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (safe_sb20) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (increase (total-cost) 0))
 )
 (:action copy_2_take_image_rover0_waypoint0_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (safe_sb19) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_2_take_image_rover0_waypoint1_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (safe_sb19) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_2_take_image_rover0_waypoint2_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (safe_sb19) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_2_take_image_rover0_waypoint3_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (safe_sb19) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_3_take_image_rover0_waypoint0_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint0) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_3_take_image_rover0_waypoint1_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint1) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_3_take_image_rover0_waypoint2_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint2) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_3_take_image_rover0_waypoint3_objective1_camera0_high_res
  :parameters ()
  :precondition (and (at_rover0_waypoint3) (calibrated_camera0_rover0) (normal-mode))
  :effect (and (have_image_rover0_objective1_high_res) (safe_sb17) (not (calibrated_camera0_rover0)) (not (ok_sb19)) (not (ok_sb20)) (increase (total-cost) 0))
 )
 (:action copy_0_sat_a0
  :parameters ()
  :precondition (and (ok_a0))
  :effect (and (a0) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_a0
  :parameters ()
  :precondition (and)  :effect (and (a0) (not (normal-mode)) (increase (total-cost) 16.53))
 )
 (:action copy_0_sat_a1
  :parameters ()
  :precondition (and (ok_a1) (a0))
  :effect (and (a1) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_a1
  :parameters ()
  :precondition (and (a0))
  :effect (and (a1) (not (normal-mode)) (increase (total-cost) 8.208))
 )
 (:action copy_0_sat_e0
  :parameters ()
  :precondition (and (ok_e0) (a1))
  :effect (and (e0) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_e0
  :parameters ()
  :precondition (and (a1))
  :effect (and (e0) (not (normal-mode)) (increase (total-cost) 12.35))
 )
 (:action copy_0_sat_e1
  :parameters ()
  :precondition (and (ok_e1) (e0))
  :effect (and (e1) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_e1
  :parameters ()
  :precondition (and (e0))
  :effect (and (e1) (not (normal-mode)) (increase (total-cost) 10.2093))
 )
 (:action copy_0_sat_e2
  :parameters ()
  :precondition (and (ok_e2) (e1))
  :effect (and (e2) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_e2
  :parameters ()
  :precondition (and (e1))
  :effect (and (e2) (not (normal-mode)) (increase (total-cost) 10.0447))
 )
 (:action copy_0_sat_o0
  :parameters ()
  :precondition (and (ok_o0) (e2))
  :effect (and (o0) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_o0
  :parameters ()
  :precondition (and (e2))
  :effect (and (o0) (not (normal-mode)) (increase (total-cost) 9.804))
 )
 (:action copy_0_sat_o1
  :parameters ()
  :precondition (and (ok_o1) (o0))
  :effect (and (o1) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_o1
  :parameters ()
  :precondition (and (o0))
  :effect (and (o1) (not (normal-mode)) (increase (total-cost) 5.434))
 )
 (:action copy_0_sat_o2
  :parameters ()
  :precondition (and (ok_o2) (o1))
  :effect (and (o2) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_o2
  :parameters ()
  :precondition (and (o1))
  :effect (and (o2) (not (normal-mode)) (increase (total-cost) 8.55))
 )
 (:action copy_0_sat_o3
  :parameters ()
  :precondition (and (ok_o3) (o2))
  :effect (and (o3) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_o3
  :parameters ()
  :precondition (and (o2))
  :effect (and (o3) (not (normal-mode)) (increase (total-cost) 9.55067))
 )
 (:action copy_0_sat_sb3
  :parameters ()
  :precondition (and (ok_sb3) (o3))
  :effect (and (sb3) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb3
  :parameters ()
  :precondition (and (o3))
  :effect (and (sb3) (not (normal-mode)) (increase (total-cost) 9.22133))
 )
 (:action copy_0_sat_sb7
  :parameters ()
  :precondition (and (ok_sb7) (sb3))
  :effect (and (sb7) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb7
  :parameters ()
  :precondition (and (sb3))
  :effect (and (sb7) (not (normal-mode)) (increase (total-cost) 4.28133))
 )
 (:action copy_0_sat_sb8
  :parameters ()
  :precondition (and (ok_sb8) (sb7))
  :effect (and (sb8) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb8
  :parameters ()
  :precondition (and (sb7))
  :effect (and (sb8) (not (normal-mode)) (increase (total-cost) 5.10467))
 )
 (:action copy_0_sat_sb11
  :parameters ()
  :precondition (and (ok_sb11) (sb8))
  :effect (and (sb11) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb11
  :parameters ()
  :precondition (and (sb8))
  :effect (and (sb11) (not (normal-mode)) (increase (total-cost) 10.0447))
 )
 (:action copy_0_sat_sb12
  :parameters ()
  :precondition (and (ok_sb12) (sb11))
  :effect (and (sb12) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb12
  :parameters ()
  :precondition (and (sb11))
  :effect (and (sb12) (not (normal-mode)) (increase (total-cost) 7.41))
 )
 (:action copy_0_sat_sb13
  :parameters ()
  :precondition (and (ok_sb13) (sb12))
  :effect (and (sb13) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb13
  :parameters ()
  :precondition (and (sb12))
  :effect (and (sb13) (not (normal-mode)) (increase (total-cost) 11.856))
 )
 (:action copy_0_sat_sb16
  :parameters ()
  :precondition (and (ok_sb16) (sb13))
  :effect (and (sb16) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb16
  :parameters ()
  :precondition (and (sb13))
  :effect (and (sb16) (not (normal-mode)) (increase (total-cost) 7.32767))
 )
 (:action copy_0_sat_sb17
  :parameters ()
  :precondition (and (ok_sb17) (sb16))
  :effect (and (sb17) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb17
  :parameters ()
  :precondition (and (sb16))
  :effect (and (sb17) (not (normal-mode)) (increase (total-cost) 9.96233))
 )
 (:action copy_0_sat_sb19
  :parameters ()
  :precondition (and (ok_sb19) (sb17))
  :effect (and (sb19) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb19
  :parameters ()
  :precondition (and (sb17))
  :effect (and (sb19) (not (normal-mode)) (increase (total-cost) 6.726))
 )
 (:action copy_0_sat_sb20
  :parameters ()
  :precondition (and (ok_sb20) (sb19))
  :effect (and (sb20) (not (normal-mode)) (increase (total-cost) 0))
 )
 (:action copy_0_unsat_sb20
  :parameters ()
  :precondition (and (sb19))
  :effect (and (sb20) (not (normal-mode)) (increase (total-cost) 14.592))
 )
)
