{"instruction": "We detail name, description, input(parameters) and output(returns) of each action as follows:\nName: get_search_movie(movie_name)\nDescription: Search for a movie by name and return basic details\nParameters:\n- movie_name (Type: string): The name of the movie to search for.\nReturns:\n- id : The ID of the found movie.\n- overview : The overview description of the movie.\n- title : The title of the movie.\n\nName: get_movie_details(movie_id)\nDescription: Get detailed information about a movie by ID\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- budget : The budget of the movie.\n- genres : The genres of the movie.\n- revenue : The revenue of the movie.\n- vote_average : The average vote score of the movie.\n- release_date : The release date of the movie.\n\nName: get_movie_production_companies(movie_id)\nDescription: Get the production companies of a movie by its ID\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- production_companies : The production companies of the movie.\n\nName: get_movie_production_countries(movie_id)\nDescription: Get the production countries of a movie by its ID\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- production_countries : The production countries of the movie.\n\nName: get_movie_cast(movie_id)\nDescription: Retrieve the list of the top 10 cast members from a movie by its ID.\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- cast : List of the top 10 cast members.\n\nName: get_movie_crew(movie_id)\nDescription: Retrieve the list of crew members (limited to 10) from a movie by its ID. The list primarily includes Director, Producer, and Writer roles.\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- crew : List of the top 10 of crew members\n\nName: get_movie_keywords(movie_id)\nDescription: Get the keywords associated with a movie by ID\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- keywords : The keywords associated with the movie.\n\nName: get_search_person(person_name)\nDescription: Search for a person by name.\nParameters:\n- person_name (Type: string): The name of the person to search for.\nReturns:\n- id : The ID of the found person.\n- name : The name of the person.\n\nName: get_person_details(person_id)\nDescription: Get detailed information about a person by ID\nParameters:\n- person_id (Type: string): The ID of the person.\nReturns:\n- biography : The biography of the person.\n- birthday : The birthday of the person.\n- place_of_birth : The place of birth of the person.\n\nName: get_person_cast(person_id)\nDescription: Retrieve the top 10 movie cast roles of a person by their ID\nParameters:\n- person_id (Type: string): The ID of the person.\nReturns:\n- cast : A list of movies where the person has acted, limited to top 10\n\nName: get_person_crew(person_id)\nDescription: Retrieve the top 10 movie crew roles of a person by their ID\nParameters:\n- person_id (Type: string): The ID of the person.\nReturns:\n- crew : A list of movies where the person has participated as crew, limited to top 10\n\nName: get_person_external_ids(person_id)\nDescription: Get the external ids for a person by ID\nParameters:\n- person_id (Type: string): The ID of the person.\nReturns:\n- imdb_id : The IMDB id of the person.\n- facebook_id : The Facebook id of the person.\n- instagram_id : The Instagram id of the person.\n- twitter_id : The Twitter id of the person.\n\nName: get_movie_alternative_titles(movie_id)\nDescription: Get the alternative titles for a movie by ID\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- titles : The alternative titles of the movie.\n- id : The ID of the movie.\n\nName: get_movie_translation(movie_id)\nDescription: Get the description translation for a movie by ID\nParameters:\n- movie_id (Type: string): The ID of the movie.\nReturns:\n- translations : The description translation of the movie.\n- id : The ID of the movie.\n\nName: check_valid_actions()\nDescription: Get supported actions for current tool.\nReturns:\n- actions (Type: array): Supported actions for current tool.\n\nName: finish(answer)\nDescription: Return an answer and finish the task\nParameters:\n- answer (Type: ['string', 'number', 'array']): The answer to be returned\n\n\nIf you are finished, you will call \"finish\" action\nPlease refer to the format of examples below to solve the requested goal. Your response must be in the format of \"Action: [your action] with Action Input: [your action input]\"", "examples": ["Goal: When did the movie Scream 6 come out?\n\nAction: get_search_movie with Action Input: {\"movie_name\": \"Scream 6\"}\nObservation: {'id': 934433, 'overview': 'Following the latest Ghostface killings, the four survivors leave Woodsboro behind and start a fresh chapter.', 'title': 'Scream VI'}\nAction: get_movie_details with Action Input: {\"movie_id\": \"934433\"}\nObservation: {'budget': 35000000, 'genres': [{'id': 27, 'name': 'Horror'}, {'id': 53, 'name': 'Thriller'}, {'id': 9648, 'name': 'Mystery'}], 'revenue': 168961389, 'vote_average': 7.175, 'release_date': '2023-03-08'}\nAction: finish with Action Input: {\"answer\": \"2023-03-08\"}\nObservation: 2023-03-08\n"], "system_msg": "You can use actions to help people solve problems.\n"}