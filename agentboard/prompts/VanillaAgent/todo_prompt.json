{"instruction": "We detail name, description, input(parameters) and output(returns) of each action as follows:\nName: get_user_current_date()\nDescription: Get the user's current date.\nReturns:\nThe current date in 'YYYY-MM-DD' format.\n\nName: get_user_current_location()\nDescription: Get the user's current city.\nReturns:\nThe user's current city.\n\nName: get_projects()\nDescription: Get all projects in the Todoist account\nReturns:\n- Array of objects with properties:\n  - id (Type: string)\n  - name (Type: string)\n  - order (Type: integer)\n  - color (Type: string)\n  - is_favorite (Type: boolean)\n\nName: update_project(project_id, is_favorite)\nDescription: Update a project\nParameters:\n- project_id (Type: string)\n- is_favorite (Type: string, Enum: [True, False])\nReturns:\nInformation of the updated project\n\nName: get_tasks(project_id)\nDescription: Get all tasks for a given project\nParameters:\n- project_id (Type: string)\nReturns:\n- Array of objects with properties:\n  - id (Type: string)\n  - project_id (Type: string)\n  - order (Type: integer)\n  - content (Type: string): Name of the task.\n  - is_completed (Type: boolean)\n  - priority (Type: integer): Task priority from 1 (normal) to 4 (urgent).\n  - due_date (Type: string): The due date of the task.\n\nName: get_task_description(task_id)\nDescription: Get the description of a specific task in the Todoist account.\nParameters:\n- task_id (Type: string)\nReturns:\n- id (Type: string): Unique identifier of the task.\n- content (Type: string): Name of the task.\n- description (Type: string): Description of the task. Incluing the Place, Tips, etc.\n\nName: get_task_duration(task_id)\nDescription: Get the duration of a specific task in the Todoist account.\nParameters:\n- task_id (Type: string)\nReturns:\n- id (Type: string)\n- content (Type: string): Name of the task.\n- duration (Type: string): Duration of the task in the format of 'amount(unit)'.\n\nName: complete_task(task_id)\nDescription: Mark a task as completed\nParameters:\n- task_id (Type: string)\nReturns:\ninformation of the completed task\n\nName: update_task(task_id, due_date)\nDescription: Update a task\nParameters:\n- task_id (Type: string)\n- due_date (Type: string)\nReturns:\nInformation of the updated task\n\nName: delete_task(task_id)\nDescription: Delete a specific task from the Todoist account.\nParameters:\n- task_id (Type: string): Unique identifier of the task to delete.\nReturns:\nInformation of the deleted task.\n\nName: check_valid_actions()\nDescription: Get supported actions for current tool.\nReturns:\nSupported actions for current tool.\n\nName: finish(answer)\nDescription: Call this action, when find the answer for the current task or complete essential operations.\nParameters:\n- answer (Type: ['string', 'number', 'array']): If the task is a question answering task, this is the answer to be returned. If the task is an operation task, the answer in 'done'\n\n\nIf you are finished, you will call \"finish\" action\nPlease refer to the format of examples below to solve the requested goal. Your response must be in the format of \"Action: [your action] with Action Input: [your action input]\"", "examples": ["Goal: Is <PERSON><PERSON>e for history quiz a task of School project? Please answer yes or no.\n\nAction: get_projects with Action Input: {}\nObservation: [{'id': '12345', 'order': 0, 'color': 'charcoal', 'name': 'School', 'is_favorite': false}]\nAction: get_tasks with Action Input: {\"project_id\": \"12345\"}\nObservation: [{'id': '123451', 'order': 0, 'content': 'Prepare for history quiz', 'is_completed': false, 'priority': 1, 'due_date': '2030-10-10'}, {'id': '123452', 'order': 1, 'content': 'Prepare for math quiz', 'is_completed': false, 'priority': 1, 'due_date': '2030-11-10'}]\nAction: finish with Action Input: {\"answer\": \"yes\"}\nObservation: yes\n"], "system_msg": "You can use actions to help people solve problems."}