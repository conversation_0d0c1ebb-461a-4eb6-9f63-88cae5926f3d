#!/usr/bin/env python3
"""
简单的AgentBoard演示脚本
测试基本的智能体和LLM功能，不依赖复杂的环境
"""

import os
import sys
import yaml

# 添加agentboard到路径
sys.path.append('agentboard')

def setup_environment():
    """设置环境变量"""
    os.environ['OPENAI_API_KEY'] = "sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb"
    os.environ['OPENAI_API_BASE'] = "https://api.chatanywhere.tech/v1"
    os.environ['PROJECT_PATH'] = os.getcwd()
    os.environ['WANDB_MODE'] = "disabled"

def test_llm_basic():
    """测试基本的LLM功能"""
    print("=== 测试LLM基本功能 ===")
    
    try:
        from llm import load_llm
        
        # LLM配置
        llm_config = {
            "name": "gpt",
            "engine": "gpt-3.5-turbo",
            "context_length": 4096,
            "use_azure": False,
            "temperature": 0.0,
            "top_p": 1,
            "retry_delays": 20,
            "max_retry_iters": 15,
            "max_tokens": 100,
            "stop": "\n",
            "use_parser": False
        }
        
        # 加载LLM
        llm = load_llm("gpt", llm_config)
        print("✅ LLM加载成功")
        
        # 测试生成
        system_message = "你是一个有用的助手。"
        prompt = "请简单介绍一下人工智能。"
        
        success, response = llm.generate(system_message, prompt)
        
        if success:
            print("✅ LLM生成成功")
            print(f"回复: {response}")
            return llm
        else:
            print("❌ LLM生成失败")
            return None
            
    except Exception as e:
        print(f"❌ LLM测试失败: {str(e)}")
        return None

def test_agent_basic(llm):
    """测试基本的智能体功能"""
    print("\n=== 测试智能体基本功能 ===")
    
    try:
        from agents import load_agent
        
        # 智能体配置
        agent_config = {
            "name": "VanillaAgent",
            "memory_size": 100,
            "need_goal": True
        }
        
        # 加载智能体
        agent = load_agent("VanillaAgent", agent_config, llm)
        print("✅ 智能体加载成功")
        
        # 测试智能体推理
        goal = "回答用户关于天气的问题"
        observation = "用户问：今天天气怎么样？"
        
        # 重置智能体
        agent.reset(goal, observation)
        print("✅ 智能体重置成功")
        
        # 运行智能体
        success, response = agent.run()
        
        if success:
            print("✅ 智能体运行成功")
            print(f"智能体回复: {response}")
            return True
        else:
            print("❌ 智能体运行失败")
            return False
            
    except Exception as e:
        print(f"❌ 智能体测试失败: {str(e)}")
        return False

def test_simple_conversation():
    """测试简单的对话场景"""
    print("\n=== 测试简单对话场景 ===")
    
    try:
        from llm import load_llm
        from agents import load_agent
        
        # 配置
        llm_config = {
            "name": "gpt",
            "engine": "gpt-3.5-turbo",
            "context_length": 4096,
            "use_azure": False,
            "temperature": 0.1,
            "max_tokens": 150,
            "stop": "\n\n"
        }
        
        agent_config = {
            "name": "VanillaAgent",
            "memory_size": 100,
            "need_goal": True
        }
        
        # 加载组件
        llm = load_llm("gpt", llm_config)
        agent = load_agent("VanillaAgent", agent_config, llm)
        
        # 对话场景
        scenarios = [
            {
                "goal": "帮助用户解决数学问题",
                "observation": "用户问：2+2等于多少？",
                "expected_type": "数学计算"
            },
            {
                "goal": "提供编程建议",
                "observation": "用户问：如何学习Python编程？",
                "expected_type": "编程建议"
            },
            {
                "goal": "回答常识问题",
                "observation": "用户问：地球有几个月亮？",
                "expected_type": "常识回答"
            }
        ]
        
        success_count = 0
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n--- 场景 {i}: {scenario['expected_type']} ---")
            print(f"目标: {scenario['goal']}")
            print(f"观察: {scenario['observation']}")
            
            try:
                # 重置并运行智能体
                agent.reset(scenario['goal'], scenario['observation'])
                success, response = agent.run()
                
                if success and response:
                    print(f"✅ 智能体回复: {response}")
                    success_count += 1
                else:
                    print("❌ 智能体回复失败")
                    
            except Exception as e:
                print(f"❌ 场景测试失败: {str(e)}")
        
        print(f"\n=== 对话测试总结 ===")
        print(f"成功场景: {success_count}/{len(scenarios)}")
        
        if success_count == len(scenarios):
            print("🎉 所有对话场景测试通过！")
            return True
        else:
            print("⚠️ 部分对话场景测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 对话测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("AgentBoard 简单演示")
    print("=" * 50)
    
    # 设置环境
    setup_environment()
    print("✅ 环境变量设置完成")
    
    # 测试LLM
    llm = test_llm_basic()
    if not llm:
        print("❌ LLM测试失败，无法继续")
        return
    
    # 测试智能体
    agent_success = test_agent_basic(llm)
    if not agent_success:
        print("❌ 智能体测试失败")
        return
    
    # 测试对话场景
    conversation_success = test_simple_conversation()
    
    print("\n" + "=" * 50)
    if conversation_success:
        print("🎉 AgentBoard基本功能测试完成！")
        print("\n您的中转API配置正确，可以正常使用AgentBoard的核心功能。")
        print("\n接下来您可以：")
        print("1. 安装更多依赖来运行完整的评估任务")
        print("2. 自定义智能体和任务")
        print("3. 探索更复杂的评估场景")
    else:
        print("⚠️ 部分功能测试失败，请检查配置")

if __name__ == "__main__":
    main()
