#!/usr/bin/env python3
"""
测试工具API连接的脚本
"""

import os
import requests
from dotenv import load_dotenv

def test_movie_api():
    """测试电影API (TMDb)"""
    print("=== 测试电影API (TMDb) ===")
    
    load_dotenv()
    api_key = os.getenv('MOVIE_KEY')
    
    if not api_key:
        print("❌ MOVIE_KEY未设置")
        print("请在.env文件中添加: MOVIE_KEY=your_tmdb_api_key")
        print("获取地址: https://www.themoviedb.org/settings/api")
        return False
    
    # 测试TMDb API
    url = f"https://api.themoviedb.org/3/movie/popular?api_key={api_key}"
    
    try:
        print(f"正在测试API密钥: {api_key[:10]}...")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            movies = data.get('results', [])
            print(f"✅ 电影API连接成功!")
            print(f"获取到 {len(movies)} 部热门电影")
            if movies:
                print(f"示例电影: {movies[0].get('title', 'Unknown')}")
            return True
        elif response.status_code == 401:
            print(f"❌ 电影API认证失败: 无效的API密钥")
            return False
        else:
            print(f"❌ 电影API错误: HTTP {response.status_code}")
            print(f"响应: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 电影API连接超时")
        return False
    except Exception as e:
        print(f"❌ 电影API连接失败: {e}")
        return False

def test_weather_api():
    """测试天气API (OpenWeatherMap)"""
    print("\n=== 测试天气API (OpenWeatherMap) ===")
    
    load_dotenv()
    api_key = os.getenv('WEATHER_KEY')
    
    if not api_key:
        print("❌ WEATHER_KEY未设置")
        print("请在.env文件中添加: WEATHER_KEY=your_openweathermap_api_key")
        print("获取地址: https://openweathermap.org/api")
        return False
    
    # 测试OpenWeatherMap API
    url = f"http://api.openweathermap.org/data/2.5/weather?q=Beijing&appid={api_key}&units=metric"
    
    try:
        print(f"正在测试API密钥: {api_key[:10]}...")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            city = data.get('name', 'Unknown')
            temp = data.get('main', {}).get('temp', 'Unknown')
            weather = data.get('weather', [{}])[0].get('description', 'Unknown')
            
            print(f"✅ 天气API连接成功!")
            print(f"城市: {city}")
            print(f"温度: {temp}°C")
            print(f"天气: {weather}")
            return True
        elif response.status_code == 401:
            print(f"❌ 天气API认证失败: 无效的API密钥")
            return False
        else:
            print(f"❌ 天气API错误: HTTP {response.status_code}")
            print(f"响应: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 天气API连接超时")
        return False
    except Exception as e:
        print(f"❌ 天气API连接失败: {e}")
        return False

def create_env_template():
    """创建.env文件模板"""
    print("\n=== 创建.env文件模板 ===")
    
    env_content = """# AgentBoard API配置文件
# 请填入您的API密钥

# OpenAI API (已配置)
OPENAI_API_KEY=sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb
OPENAI_API_BASE=https://api.chatanywhere.tech/v1

# 电影API (TMDb) - 必需用于工具任务
# 获取地址: https://www.themoviedb.org/settings/api
MOVIE_KEY=your_tmdb_api_key_here

# 天气API (OpenWeatherMap) - 必需用于工具任务  
# 获取地址: https://openweathermap.org/api
WEATHER_KEY=your_openweathermap_api_key_here

# 其他可选API
# Google搜索API (可选)
GOOGLE_SEARCH_API_KEY=your_google_search_key_here

# 新闻API (可选)
NEWS_API_KEY=your_news_api_key_here

# 项目路径
PROJECT_PATH=/home/<USER>/mgq/2025_5/AgentBoard
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ .env文件模板已创建")
        print("请编辑.env文件，填入您的API密钥")
        return True
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False

def show_api_guide():
    """显示API获取指南"""
    print("\n=== API密钥获取指南 ===")
    print()
    print("🎬 电影API (TMDb) - 免费")
    print("1. 访问: https://www.themoviedb.org/")
    print("2. 注册账号并登录")
    print("3. 进入: 设置 → API → 申请API密钥")
    print("4. 选择 'Developer' 类型")
    print("5. 填写应用信息（可以填写学习用途）")
    print("6. 获得API密钥后填入.env文件的MOVIE_KEY")
    print()
    print("🌤️  天气API (OpenWeatherMap) - 免费")
    print("1. 访问: https://openweathermap.org/")
    print("2. 注册账号并登录")
    print("3. 进入: API keys")
    print("4. 复制默认API密钥或创建新的")
    print("5. 填入.env文件的WEATHER_KEY")
    print()
    print("💡 提示:")
    print("- TMDb API免费版每天40,000次请求")
    print("- OpenWeatherMap免费版每分钟60次调用")
    print("- 注册后API密钥可能需要几分钟才能激活")

def main():
    """主函数"""
    print("AgentBoard 工具API测试脚本")
    print("=" * 50)
    
    # 检查.env文件是否存在
    if not os.path.exists('.env'):
        print("未找到.env文件，正在创建模板...")
        create_env_template()
        show_api_guide()
        print("\n请先配置.env文件中的API密钥，然后重新运行此脚本")
        return
    
    # 测试API
    movie_success = test_movie_api()
    weather_success = test_weather_api()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"电影API: {'✅ 成功' if movie_success else '❌ 失败'}")
    print(f"天气API: {'✅ 成功' if weather_success else '❌ 失败'}")
    
    if movie_success and weather_success:
        print("\n🎉 所有工具API测试通过!")
        print("您现在可以运行需要工具API的AgentBoard任务:")
        print("python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query --model gpt-3.5-turbo")
    else:
        print("\n⚠️  部分API测试失败")
        if not movie_success or not weather_success:
            print("请检查API密钥配置，参考上面的获取指南")
            show_api_guide()

if __name__ == "__main__":
    main()
