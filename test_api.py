#!/usr/bin/env python3
"""
测试中转OpenAI API连接的脚本
"""

import os
import openai
import sys

def test_openai_api():
    """测试OpenAI API连接"""

    # 设置API密钥
    api_key = "sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb"
    openai.api_key = api_key

    # 设置中转API地址
    openai.api_base = "https://api.chatanywhere.tech/v1"

    print("=== OpenAI API 连接测试 ===")
    print(f"API Key: {api_key[:10]}...")
    print(f"API Base: {getattr(openai, 'api_base', '默认OpenAI官方API')}")

    try:
        # 测试简单的聊天完成
        print("\n正在测试 GPT-3.5-turbo...")
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "你是一个有用的助手。"},
                {"role": "user", "content": "请简单介绍一下你自己，用一句话回答。"}
            ],
            max_tokens=50,
            temperature=0
        )

        result = response.choices[0].message.content
        print(f"✅ GPT-3.5-turbo 测试成功!")
        print(f"回复: {result}")

        # 测试GPT-4（如果可用）
        print("\n正在测试 GPT-4...")
        try:
            response_gpt4 = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "你是一个有用的助手。"},
                    {"role": "user", "content": "用一句话说明你是GPT-4。"}
                ],
                max_tokens=30,
                temperature=0
            )

            result_gpt4 = response_gpt4.choices[0].message.content
            print(f"✅ GPT-4 测试成功!")
            print(f"回复: {result_gpt4}")

        except Exception as e:
            print(f"⚠️  GPT-4 测试失败: {str(e)}")
            print("这可能是因为您的API不支持GPT-4或配额限制")

        print(f"\n🎉 API连接测试完成！您可以开始使用AgentBoard进行评估。")
        return True

    except Exception as e:
        print(f"❌ API连接测试失败!")
        print(f"错误信息: {str(e)}")
        print("\n可能的解决方案:")
        print("1. 检查API密钥是否正确")
        print("2. 检查网络连接")
        print("3. 如果使用中转API，请设置正确的api_base URL")
        print("4. 检查API配额是否充足")
        return False

def test_agentboard_import():
    """测试AgentBoard模块导入"""
    print("\n=== AgentBoard 模块测试 ===")

    try:
        # 测试导入核心模块
        sys.path.append('agentboard')

        from llm import load_llm
        from agents import load_agent
        from tasks import load_task
        from common.registry import registry

        print("✅ AgentBoard 核心模块导入成功!")

        # 显示可用的组件
        print(f"可用的LLM: {registry.list_llms()}")
        print(f"可用的智能体: {registry.list_agents()}")
        print(f"可用的任务: {registry.list_tasks()}")

        return True

    except Exception as e:
        print(f"❌ AgentBoard 模块导入失败!")
        print(f"错误信息: {str(e)}")
        print("\n可能需要安装缺失的依赖:")
        print("pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    print("AgentBoard 中转API 测试脚本")
    print("=" * 50)

    # 测试API连接
    api_success = test_openai_api()

    # 测试AgentBoard模块
    module_success = test_agentboard_import()

    print("\n" + "=" * 50)
    if api_success and module_success:
        print("🎉 所有测试通过！您可以开始使用AgentBoard了。")
        print("\n推荐的第一个命令:")
        print("python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query --model gpt-3.5-turbo --log_path ./results/custom_test")
    else:
        print("⚠️  部分测试失败，请根据上述提示解决问题。")
