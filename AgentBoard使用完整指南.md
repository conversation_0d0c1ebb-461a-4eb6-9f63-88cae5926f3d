# AgentBoard 中转API使用完整指南

## 🎉 恭喜！您的AgentBoard已配置完成

您已经成功配置了AgentBoard使用中转OpenAI API，现在可以开始进行LLM智能体评估了！

## ✅ 已完成的配置

### 1. 环境配置
- ✅ AgentBoard conda环境已激活
- ✅ 基础依赖包已安装
- ✅ 中转OpenAI API已配置并测试成功
- ✅ 核心功能已验证正常

### 2. 已创建的文件
- `eval_configs/custom_openai_config.yaml` - 自定义配置文件
- `setup_custom_api.sh` - 环境配置脚本
- `test_api.py` - OpenAI API测试脚本
- `simple_agent_demo.py` - 基本功能演示脚本
- `install_quick_deps.sh` - 快速依赖安装脚本
- `test_tool_apis.py` - 工具API测试脚本
- `.env` - API密钥配置文件模板
- 各种说明文档

### 3. 当前可用功能
- ✅ LLM文本生成 (GPT-3.5-turbo, GPT-4)
- ✅ 基础智能体 (VanillaAgent)
- ✅ 多场景对话测试
- ✅ 配置文件管理
- ✅ 日志记录

## 🚀 立即可用的命令

### 1. 测试基本功能
```bash
# 设置环境变量
source setup_custom_api.sh

# 测试OpenAI API连接
python test_api.py

# 运行基本功能演示
python simple_agent_demo.py
```

### 2. 测试工具API（需要先配置API密钥）
```bash
# 测试工具API连接
python test_tool_apis.py
```

## 🔑 获取工具API密钥

要运行完整的AgentBoard评估任务，您需要获取以下API密钥：

### 必需的API密钥

#### 1. 电影API (TMDb) - 免费
- **网站**: https://www.themoviedb.org/
- **步骤**:
  1. 注册账号并登录
  2. 进入: 设置 → API → 申请API密钥
  3. 选择 'Developer' 类型
  4. 填写应用信息（可以填写学习用途）
  5. 获得API密钥后填入`.env`文件的`MOVIE_KEY`
- **配额**: 免费版每天40,000次请求

#### 2. 天气API (OpenWeatherMap) - 免费
- **网站**: https://openweathermap.org/
- **步骤**:
  1. 注册账号并登录
  2. 进入: API keys
  3. 复制默认API密钥或创建新的
  4. 填入`.env`文件的`WEATHER_KEY`
- **配额**: 免费版每分钟60次调用

### 配置API密钥
1. 编辑`.env`文件
2. 将获取的API密钥填入对应位置
3. 运行`python test_tool_apis.py`验证

## 📋 可运行的评估任务

### 1. 立即可用（无需额外API）
```bash
# 基本功能演示
python simple_agent_demo.py

# 注意：以下任务可能需要额外依赖
# python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks alfworld --model gpt-3.5-turbo
```

### 2. 配置工具API后可用
```bash
# 工具查询任务
python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query --model gpt-3.5-turbo --log_path ./results/custom_test

# 工具操作任务
python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-operation --model gpt-3.5-turbo --log_path ./results/custom_test
```

### 3. 安装额外依赖后可用
```bash
# 安装textworld后可运行ALFWorld
pip install textworld
python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks alfworld --model gpt-3.5-turbo

# 安装babyai后可运行BabyAI
pip install babyai-text
python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks babyai --model gpt-3.5-turbo
```

## 🔧 配置说明

### 您的API配置
- **OpenAI API密钥**: `sk-uHhSnfWkSNIpXAB9XlSRIuAnCEQTXTts9GKeFGRI3w2lXyeb`
- **中转API地址**: `https://api.chatanywhere.tech/v1`
- **可用模型**: gpt-3.5-turbo, gpt-4, gpt-3.5-turbo-16k

### 配置文件位置
- 主配置: `eval_configs/custom_openai_config.yaml`
- 环境变量: `.env`
- 日志输出: `./results/custom_test/`

## 📊 评估指标说明

AgentBoard会输出以下评估指标：

- **Success Rate (SR)**: 任务完全成功的比例
- **Progress Rate (PR)**: 任务完成的程度（0-1之间）
- **Grounding Accuracy**: 智能体行动与环境状态匹配的准确性
- **Easy/Hard分级**: 按任务难度分级的表现

## 🛠️ 故障排除

### 常见问题

1. **环境变量未设置**
   ```bash
   source setup_custom_api.sh
   ```

2. **API密钥错误**
   ```bash
   python test_api.py  # 测试OpenAI API
   python test_tool_apis.py  # 测试工具API
   ```

3. **依赖缺失**
   ```bash
   ./install_quick_deps.sh  # 安装基础依赖
   pip install 包名  # 按需安装特定包
   ```

4. **任务运行失败**
   - 检查日志文件: `./results/custom_test/logs/`
   - 确认API配额充足
   - 验证网络连接

### 获取帮助
- 查看详细文档: `agentboard/README_CN.md`
- 运行测试脚本验证配置
- 检查日志文件了解错误详情

## 🎯 下一步建议

### 1. 立即开始
1. 获取TMDb和OpenWeatherMap API密钥
2. 配置`.env`文件
3. 运行工具任务测试

### 2. 扩展功能
1. 安装更多依赖包支持更多任务
2. 自定义智能体和提示词
3. 调整评估参数

### 3. 深入研究
1. 分析评估结果和日志
2. 比较不同模型的表现
3. 开发自定义评估任务

## 📞 技术支持

如果遇到问题：
1. 首先运行相关测试脚本
2. 查看错误日志
3. 检查API配额和网络
4. 参考文档和配置文件

---

**祝您使用AgentBoard愉快！🚀**

您现在拥有了一个完整配置的LLM智能体评估框架，可以开始探索人工智能的无限可能！
