# AgentBoard 工具任务问题分析和解决方案

## 🔍 问题分析

### 根本原因：动作格式解析错误

通过分析 `results/custom_test/logs/tool-query.jsonl` 和控制台输出，发现了问题的根源：

#### 1. 智能体输出格式问题
- **智能体实际输出**：`"Action: loadAuthorNet with Action Input: {}"`
- **期望的正确格式**：`"loadAuthorNet with Action Input: {}"`

#### 2. 解析函数的期望格式
在 `agentboard/utils/tool/helpers.py` 中的 `extract_action_name_and_action_input()` 函数：

```python
def extract_action_name_and_action_input(text):
    pattern = r"\s*(.*?)\s*with\s*Action Input:\s*(.*?)$"
    match = re.search(pattern, text)
    if match:
        action = match.group(1)
        action_input = match.group(2)
        return action, action_input
    else:
        return None, None
```

这个函数期望的格式是：`"动作名 with Action Input: 参数"`

#### 3. 实际发生的情况
- 智能体输出：`"Action: loadAuthorNet with Action Input: {}"`
- 解析结果：`action = "Action: loadAuthorNet"`, `action_input = "{}"`
- 环境尝试执行名为 `"Action: loadAuthorNet"` 的动作
- 但实际的动作名应该是 `"loadAuthorNet"`
- 结果：`"ERROR | Invalid action: Action: loadAuthorNet."`

### 为什么进度率始终为0？

1. **所有动作都失败**：由于格式错误，没有任何动作能够成功执行
2. **环境状态不变**：智能体无法与工具环境进行有效交互
3. **任务无法完成**：无法加载数据、查询信息或完成目标
4. **达到最大步数**：智能体重复尝试相同的错误动作直到超时

## 🛠️ 解决方案

### 方案1：修改提示词模板（推荐）

修改智能体的提示词，让它输出正确的格式：

```python
# 当前提示词可能包含：
"Your response must be in the format of \"Action: [your action] with Action Input: [your action input]\""

# 应该修改为：
"Your response must be in the format of \"[your action] with Action Input: [your action input]\""
```

### 方案2：修改解析函数

修改 `extract_action_name_and_action_input()` 函数来处理两种格式：

```python
def extract_action_name_and_action_input(text):
    # 尝试匹配 "Action: 动作名 with Action Input: 参数" 格式
    pattern1 = r"Action:\s*(.*?)\s*with\s*Action Input:\s*(.*?)$"
    match1 = re.search(pattern1, text)
    if match1:
        action = match1.group(1)
        action_input = match1.group(2)
        return action, action_input
    
    # 尝试匹配 "动作名 with Action Input: 参数" 格式
    pattern2 = r"\s*(.*?)\s*with\s*Action Input:\s*(.*?)$"
    match2 = re.search(pattern2, text)
    if match2:
        action = match2.group(1)
        action_input = match2.group(2)
        return action, action_input
    
    return None, None
```

### 方案3：修改智能体输出处理

在智能体的输出处理中添加格式清理：

```python
def clean_action_format(message):
    # 如果消息以 "Action: " 开头，移除它
    if message.strip().startswith("Action: "):
        return message.strip()[8:]  # 移除 "Action: "
    return message.strip()
```

## 🔧 立即修复

让我们实施方案2，修改解析函数来兼容两种格式：

### 修改文件：`agentboard/utils/tool/helpers.py`

```python
def extract_action_name_and_action_input(text):
    # 首先尝试匹配 "Action: 动作名 with Action Input: 参数" 格式
    pattern1 = r"Action:\s*(.*?)\s*with\s*Action Input:\s*(.*?)$"
    match1 = re.search(pattern1, text, re.IGNORECASE)
    if match1:
        action = match1.group(1).strip()
        action_input = match1.group(2).strip()
        return action, action_input
    
    # 然后尝试匹配原始的 "动作名 with Action Input: 参数" 格式
    pattern2 = r"\s*(.*?)\s*with\s*Action Input:\s*(.*?)$"
    match2 = re.search(pattern2, text, re.IGNORECASE)
    if match2:
        action = match2.group(1).strip()
        action_input = match2.group(2).strip()
        return action, action_input
    
    return None, None
```

## 📊 预期效果

修复后，智能体应该能够：

1. **成功执行动作**：
   - `loadAuthorNet` → `"AuthorNet is loaded."`
   - `loadPaperNet` → `"PaperNet is loaded."`

2. **正常查询数据**：
   - `authorNodeCheck` → 返回作者信息
   - `neighbourCheck` → 返回邻居节点

3. **完成任务目标**：
   - 进度率 > 0
   - 部分或完全成功的任务

4. **提高评估指标**：
   - Success Rate > 0%
   - Progress Rate > 0
   - Grounding Accuracy > 0%

## 🧪 测试验证

修复后可以通过以下方式验证：

1. **重新运行工具任务**：
   ```bash
   python agentboard/eval_main.py --cfg-path eval_configs/custom_openai_config.yaml --tasks tool-query --model gpt-3.5-turbo --log_path ./results/custom_test_fixed
   ```

2. **检查日志输出**：
   - 查看是否有成功的动作执行
   - 观察进度率是否 > 0
   - 确认智能体能够获取有效的观察结果

3. **对比结果**：
   - 修复前：所有任务进度率 = 0
   - 修复后：应该有部分任务进度率 > 0

## 💡 长期优化建议

1. **统一动作格式**：在所有任务中使用一致的动作格式规范
2. **改进提示词**：提供更清晰的动作格式示例
3. **增强错误处理**：提供更详细的格式错误提示
4. **添加格式验证**：在动作执行前验证格式正确性

这个修复应该能够解决当前工具任务中进度率为0的问题，让AgentBoard能够正常评估LLM智能体的工具使用能力。
